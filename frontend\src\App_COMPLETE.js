          <div className="space-y-6">
            <div className={`rounded-lg shadow-sm border p-6 transition-colors duration-200 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
              <h2 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${darkMode ? 'text-white' : 'text-gray-900'}`}>Hosting Services</h2>
              <div className="grid grid-cols-1 gap-3">
                {[{name: 'Lulustream', key: 'lulustream'}, {name: 'StreamP2P', key: 'streamp2p'}, {name: 'RPMShare', key: 'rpmshare'}, {name: 'Filemoon', key: 'filemoon'}, {name: 'UpnShare', key: 'upnshare'}].map(service => {
                  const serviceData = systemStatus?.services?.[service.key];
                  const isConfigured = serviceData?.configured;
                  const activeUploads = serviceData?.active_uploads || 0;
                  const progress = serviceData?.progress;
                  return (
                    <div key={service.key} className={`p-3 rounded-md transition-colors duration-200 ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                      <div className="flex items-center justify-between mb-2">
                        <span className={`font-medium transition-colors duration-200 ${darkMode ? 'text-white' : 'text-gray-900'}`}>{service.name}</span>
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full ${isConfigured ? activeUploads > 0 ? 'bg-blue-500' : 'bg-green-500' : 'bg-gray-400'}`}></div>
                          <span className={`text-sm transition-colors duration-200 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                            {isConfigured ? activeUploads > 0 ? `Uploading (${activeUploads})` : 'Ready' : 'Not Configured'}
                          </span>
                        </div>
                      </div>
                      {isConfigured && activeUploads > 0 && progress && (
                        <div className="mt-2">
                          <div className={`w-full bg-gray-200 rounded-full h-2 ${darkMode ? 'bg-gray-600' : 'bg-gray-200'}`}>
                            <div className="bg-blue-600 h-2 rounded-full transition-all duration-300" style={{ width: `${progress.percentage || 0}%` }}></div>
                          </div>
                          <div className={`text-xs mt-1 transition-colors duration-200 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            {progress.percentage ? `${progress.percentage}%` : 'Processing...'}{progress.speed && ` • ${formatSpeed(progress.speed)}`}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
              <p className={`text-xs mt-3 transition-colors duration-200 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                {systemStatus?.services_configured > 0 ? `${systemStatus.services_configured}/5 services configured and ready` : 'Configure API keys in the .env file to activate services'}
              </p>
            </div>
          </div>
        </div>

        <div className={`rounded-lg shadow-sm border p-6 transition-colors duration-200 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
          <h2 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${darkMode ? 'text-white' : 'text-gray-900'}`}>Upload History</h2>
          {uploads.length === 0 ? (
            <p className={`text-center py-8 transition-colors duration-200 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>No uploads yet</p>
          ) : (
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {uploads.map(upload => (
                <div key={upload.upload_id} className={`border rounded-lg p-4 transition-colors duration-200 ${darkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-white'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                      <h3 className={`font-medium truncate transition-colors duration-200 ${darkMode ? 'text-white' : 'text-gray-900'}`}>{upload.filename}</h3>
                      <button onClick={() => openTempFolder(upload.upload_id)} className={`p-1 rounded hover:bg-gray-200 transition-colors duration-200 ${darkMode ? 'hover:bg-gray-600 text-gray-400' : 'hover:bg-gray-200 text-gray-500'}`} title="Open temporary folder">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z" />
                        </svg>
                      </button>
                    </div>
                    <span className={`text-sm font-medium ${getStatusColor(upload.status)}`}>{upload.status.charAt(0).toUpperCase() + upload.status.slice(1)}</span>
                  </div>
                  <div className={`text-sm mb-3 transition-colors duration-200 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    <p>Size: {formatFileSize(upload.file_size)}</p>
                    <p>Created: {new Date(upload.created_at).toLocaleString()}</p>
                  </div>
                  {upload.final_results && (
                    <div className="grid grid-cols-5 gap-2 mb-3">
                      {['lulustream', 'streamp2p', 'rpmshare', 'filemoon', 'upnshare'].map(service => (
                        <div key={service} className={`text-center p-2 rounded text-xs ${upload.final_results[service] && upload.final_results[service].embed_code ? darkMode ? 'bg-green-900 text-green-300' : 'bg-green-100 text-green-700' : upload.final_results[service] && upload.final_results[service].error ? darkMode ? 'bg-red-900 text-red-300' : 'bg-red-100 text-red-700' : darkMode ? 'bg-gray-600 text-gray-300' : 'bg-gray-100 text-gray-700'}`}>
                          {service.charAt(0).toUpperCase() + service.slice(1)}
                        </div>
                      ))}
                    </div>
                  )}
                  <div className="flex items-center justify-between">
                    <div className="flex space-x-2">
                      {upload.csv_available && (
                        <button onClick={() => downloadCSV(upload.upload_id)} className={`px-3 py-1 rounded text-xs transition-colors duration-200 ${darkMode ? 'bg-blue-700 text-white hover:bg-blue-600' : 'bg-blue-600 text-white hover:bg-blue-700'}`}>Download CSV</button>
                      )}
                      <button onClick={() => showUploadLogs(upload.upload_id)} className={`px-3 py-1 rounded text-xs transition-colors duration-200 ${darkMode ? 'bg-gray-600 text-white hover:bg-gray-500' : 'bg-gray-600 text-white hover:bg-gray-700'}`}>View Logs</button>
                      {(upload.status === 'uploading' || upload.status === 'processing') && (
                        <button onClick={() => cancelUpload(upload.upload_id)} className="bg-yellow-600 text-white px-3 py-1 rounded text-xs hover:bg-yellow-700 transition-colors duration-200">Cancel</button>
                      )}
                    </div>
                    <button onClick={() => deleteUpload(upload.upload_id)} className={`text-xs transition-colors duration-200 ${darkMode ? 'text-red-400 hover:text-red-300' : 'text-red-600 hover:text-red-800'}`}>Delete</button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {showLogs && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className={`rounded-lg max-w-4xl w-full max-h-[80vh] flex flex-col transition-colors duration-200 ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
            <div className={`flex items-center justify-between p-4 border-b transition-colors duration-200 ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <div className="flex space-x-4">
                <h3 className={`text-lg font-semibold transition-colors duration-200 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {logsType === 'system' ? 'System Logs' : `Upload Logs: ${selectedUploadLogs?.upload_id}`}
                </h3>
                <div className="flex space-x-2">
                  <button onClick={() => { setLogsType('system'); fetchSystemLogs(); }} className={`px-3 py-1 rounded text-sm transition-colors duration-200 ${logsType === 'system' ? 'bg-blue-600 text-white' : darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'}`}>System</button>
                  {selectedUploadLogs && (
                    <button onClick={() => setLogsType('upload')} className={`px-3 py-1 rounded text-sm transition-colors duration-200 ${logsType === 'upload' ? 'bg-blue-600 text-white' : darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'}`}>Upload</button>
                  )}
                </div>
              </div>
              <button onClick={() => setShowLogs(false)} className={`transition-colors duration-200 ${darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'}`}>
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className="flex-1 overflow-y-auto p-4">
              <div className="space-y-2">
                {(logsType === 'system' ? systemLogs : selectedUploadLogs?.logs || []).map((log, index) => (
                  <div key={index} className="flex items-start space-x-3 text-sm font-mono">
                    <span className={`flex-shrink-0 transition-colors duration-200 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>{new Date(log.timestamp).toLocaleTimeString()}</span>
                    <span className={`flex-shrink-0 ${getLogLevelColor(log.level)}`}>[{log.level}]</span>
                    <span className={`flex-1 transition-colors duration-200 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>{log.message}</span>
                  </div>
                ))}
                {(logsType === 'system' ? systemLogs : selectedUploadLogs?.logs || []).length === 0 && (
                  <p className={`text-center py-8 transition-colors duration-200 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>No logs available</p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      <div className={`border-t mt-12 transition-colors duration-200 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <p className={`text-center text-sm transition-colors duration-200 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Video Upload Automation - Upload to multiple hosting services simultaneously</p>
        </div>
      </div>
    </div>
  );
}

export default App;