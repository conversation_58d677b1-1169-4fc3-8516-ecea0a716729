@echo off
echo ========================================
echo FIXING AIOHTTP Installation
echo ========================================

cd /d "G:\My Websites\Catalogue-Website\video-upload-automation\video-upload-automation\backend"

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo Trying different approaches to install aiohttp...

echo Method 1: Installing with specific version and pre-compiled wheel...
pip install --only-binary=all aiohttp==3.8.6

echo Method 2: If above failed, trying latest version...
pip install aiohttp

echo Method 3: If still failing, trying with no dependencies first...
pip install --no-deps aiohttp
pip install multidict yarl async-timeout aiosignal frozenlist attrs

echo Method 4: Alternative - install older stable version...
pip install aiohttp==3.8.1

echo ========================================
echo Testing aiohttp installation...
python -c "import aiohttp; print('✅ aiohttp installed successfully - Version:', aiohttp.__version__)"

echo ========================================
echo Testing ALL packages now...
python -c "import fastapi; print('✅ FastAPI:', fastapi.__version__)"
python -c "import uvicorn; print('✅ Uvicorn installed')"
python -c "import psutil; print('✅ psutil:', psutil.__version__)"
python -c "import aiofiles; print('✅ aiofiles installed')"
python -c "import aiohttp; print('✅ aiohttp:', aiohttp.__version__)"
python -c "import pymongo; print('✅ pymongo installed')"
python -c "import dotenv; print('✅ python-dotenv installed')"

echo ========================================
echo Fix completed! Now testing server startup...
echo ========================================

python -c "print('Testing server imports...'); import sys; sys.path.append('.'); from server import app; print('✅ Server imports successful!')"

echo ========================================
echo All packages are now working!
echo You can run: python server.py
echo ========================================

pause