@echo off
echo ========================================
echo QUICK FIX for Python Package Installation
echo ========================================

cd /d "G:\My Websites\Catalogue-Website\video-upload-automation\video-upload-automation\backend"

echo Activating virtual environment...
call venv\Scripts\activate.bat

echo Upgrading pip and build tools...
python -m pip install --upgrade pip
pip install --upgrade setuptools wheel

echo Installing packages with pre-compiled wheels...
pip install --only-binary=all fastapi==0.104.1
pip install --only-binary=all uvicorn[standard]==0.24.0
pip install --only-binary=all python-multipart==0.0.6
pip install --only-binary=all aiofiles==23.2.1
pip install --only-binary=all aiohttp==3.9.1
pip install --only-binary=all pymongo==4.6.0
pip install --only-binary=all python-dotenv==1.0.0

echo Installing psutil with pre-compiled wheel...
pip install --only-binary=all psutil==5.9.6

echo ========================================
echo Installation completed!
echo ========================================

echo Testing imports...
python -c "import fastapi; print('✅ FastAPI installed successfully')"
python -c "import uvicorn; print('✅ Uvicorn installed successfully')"
python -c "import psutil; print('✅ psutil installed successfully')"
python -c "import aiofiles; print('✅ aiofiles installed successfully')"
python -c "import aiohttp; print('✅ aiohttp installed successfully')"
python -c "import pymongo; print('✅ pymongo installed successfully')"
python -c "import dotenv; print('✅ python-dotenv installed successfully')"

echo ========================================
echo All packages installed successfully!
echo You can now run: python server.py
echo ========================================

pause