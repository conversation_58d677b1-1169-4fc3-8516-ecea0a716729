from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, Form, BackgroundTasks
from fastapi.responses import JSO<PERSON>esponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
import os
import asyncio
import aiohttp
import aiofiles
import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime
import csv
import io
from concurrent.futures import ThreadPoolExecutor
import psutil
import time
import tempfile
import threading
import logging
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
CORS_ORIGINS = os.environ.get('CORS_ORIGINS', '*')

# API Configuration
MAX_CONCURRENT_UPLOADS = int(os.environ.get('MAX_CONCURRENT_UPLOADS', '5'))
BANDWIDTH_LIMIT_PERCENT = int(os.environ.get('BANDWIDTH_LIMIT_PERCENT', '80'))
CHUNK_SIZE_MB = int(os.environ.get('CHUNK_SIZE_MB', '100'))

# API Keys
LULUSTREAM_API_KEY = os.environ.get('LULUSTREAM_API_KEY', '')
STREAMP2P_API_KEY = os.environ.get('STREAMP2P_API_KEY', '')
RPMSHARE_API_KEY = os.environ.get('RPMSHARE_API_KEY', '')
FILEMOON_API_KEY = os.environ.get('FILEMOON_API_KEY', '')
UPNSHARE_API_KEY = os.environ.get('UPNSHARE_API_KEY', '')

# Initialize FastAPI
app = FastAPI(title="Video Upload Automation API", version="1.0.0")

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint - API status"""
    return {
        "message": "Video Upload Automation API",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "system_status": "/api/system-status",
            "uploads": "/api/uploads",
            "upload": "/api/upload (POST)",
            "system_logs": "/api/system-logs",
            "docs": "/docs",
            "redoc": "/redoc"
        }
    }

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[CORS_ORIGINS] if CORS_ORIGINS != "*" else ["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage (replacing MongoDB)
uploads_storage = {}
upload_logs = {}
active_uploads = {}
upload_queue = []
system_logs = []
service_progress = {}  # Track individual service upload progress

# Thread locks for thread-safe operations
storage_lock = threading.Lock()
log_lock = threading.Lock()

def add_system_log(message: str, level: str = "INFO"):
    """Add system log entry"""
    with log_lock:
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': level,
            'message': message
        }
        system_logs.append(log_entry)
        # Keep only last 100 logs
        if len(system_logs) > 100:
            system_logs.pop(0)
        logger.info(f"[{level}] {message}")

def add_upload_log(upload_id: str, message: str, level: str = "INFO"):
    """Add upload-specific log entry"""
    with log_lock:
        if upload_id not in upload_logs:
            upload_logs[upload_id] = []
        
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': level,
            'message': message
        }
        upload_logs[upload_id].append(log_entry)
        # Keep only last 50 logs per upload
        if len(upload_logs[upload_id]) > 50:
            upload_logs[upload_id].pop(0)

# Global variables for upload management
upload_semaphore = asyncio.Semaphore(MAX_CONCURRENT_UPLOADS)
task_registry = {}  # Track background tasks for cancellation

class VideoHostService:
    """Base class for video hosting services"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.session = None
    
    async def get_session(self):
        if not self.session:
            connector = aiohttp.TCPConnector(
                limit=10,  # Limit connections to prevent overwhelming system
                limit_per_host=5,
                keepalive_timeout=30
            )
            timeout = aiohttp.ClientTimeout(total=3600)  # 1 hour timeout for large uploads
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            )
        return self.session
    
    async def close_session(self):
        if self.session:
            await self.session.close()
            self.session = None

class LulustreamService(VideoHostService):
    """Lulustream upload service"""
    
    async def get_upload_server(self):
        session = await self.get_session()
        url = f"https://lulustream.com/api/upload/server?key={self.api_key}"
        async with session.get(url) as response:
            data = await response.json()
            if data.get('status') == 200:
                return data['result']
            else:
                raise Exception(f"Failed to get upload server: {data}")
    
    async def upload_file(self, file_path: str, filename: str, upload_id: str, progress_callback=None):
        upload_server = await self.get_upload_server()
        session = await self.get_session()
        
        add_upload_log(upload_id, f"Starting Lulustream upload to server: {upload_server}")
        
        data = aiohttp.FormData()
        data.add_field('key', self.api_key)
        data.add_field('file_title', filename.rsplit('.', 1)[0])
        
        async with aiofiles.open(file_path, 'rb') as f:
            file_content = await f.read()
            data.add_field('file', file_content, filename=filename)
        
        async with session.post(upload_server, data=data) as response:
            result = await response.json()
            if result.get('status') == 200 and result.get('files'):
                filecode = result['files'][0]['filecode']
                embed_code = f'<iframe src="https://luluvid.com/e/{filecode}" scrolling="no" frameborder="0" width="640" height="360" allowfullscreen="true" webkitallowfullscreen="true" mozallowfullscreen="true"></iframe>'
                add_upload_log(upload_id, f"Lulustream upload successful. Filecode: {filecode}", "SUCCESS")
                return {'filecode': filecode, 'embed_code': embed_code}
            else:
                raise Exception(f"Upload failed: {result}")

class StreamP2PService(VideoHostService):
    """StreamP2P upload service"""
    
    async def get_upload_endpoint(self):
        session = await self.get_session()
        headers = {'Authorization': f'Bearer {self.api_key}'}
        url = "https://streamp2p.com/api/v1/video/upload"
        
        async with session.get(url, headers=headers) as response:
            data = await response.json()
            return data.get('upload_url', 'https://streamp2p.com/upload')
    
    async def upload_file(self, file_path: str, filename: str, upload_id: str, progress_callback=None):
        upload_url = await self.get_upload_endpoint()
        session = await self.get_session()
        
        add_upload_log(upload_id, f"Starting StreamP2P upload to: {upload_url}")
        
        headers = {'Authorization': f'Bearer {self.api_key}'}
        data = aiohttp.FormData()
        
        async with aiofiles.open(file_path, 'rb') as f:
            file_content = await f.read()
            data.add_field('file', file_content, filename=filename)
        
        async with session.post(upload_url, data=data, headers=headers) as response:
            result = await response.json()
            if result.get('success'):
                video_id = result.get('video_id', 'unknown')
                embed_code = f'<iframe src="https://streamdb.p2pstream.online/#{video_id}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>'
                add_upload_log(upload_id, f"StreamP2P upload successful. Video ID: {video_id}", "SUCCESS")
                return {'video_id': video_id, 'embed_code': embed_code}
            else:
                raise Exception(f"Upload failed: {result}")

class RPMShareService(VideoHostService):
    """RPMShare upload service"""
    
    async def upload_file(self, file_path: str, filename: str, upload_id: str, progress_callback=None):
        session = await self.get_session()
        headers = {'Authorization': f'Bearer {self.api_key}'}
        url = "https://rpmshare.com/api/v1/video/upload"
        
        add_upload_log(upload_id, f"Starting RPMShare upload")
        
        data = aiohttp.FormData()
        async with aiofiles.open(file_path, 'rb') as f:
            file_content = await f.read()
            data.add_field('file', file_content, filename=filename)
        
        async with session.post(url, data=data, headers=headers) as response:
            result = await response.json()
            if result.get('success'):
                video_id = result.get('video_id', 'unknown')
                embed_code = f'<iframe src="https://streamdb.rpmstream.online/#{video_id}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>'
                add_upload_log(upload_id, f"RPMShare upload successful. Video ID: {video_id}", "SUCCESS")
                return {'video_id': video_id, 'embed_code': embed_code}
            else:
                raise Exception(f"Upload failed: {result}")

class FilemoonService(VideoHostService):
    """Filemoon upload service"""
    
    async def upload_file(self, file_path: str, filename: str, upload_id: str, progress_callback=None):
        session = await self.get_session()
        url = "https://filemoon.to/api/upload"
        
        add_upload_log(upload_id, f"Starting Filemoon upload")
        
        data = aiohttp.FormData()
        data.add_field('api_key', self.api_key)
        
        async with aiofiles.open(file_path, 'rb') as f:
            file_content = await f.read()
            data.add_field('file', file_content, filename=filename)
        
        async with session.post(url, data=data) as response:
            result = await response.json()
            if result.get('success'):
                file_id = result.get('file_id', 'unknown')
                embed_code = f'<iframe src="https://filemoon.to/e/{file_id}/{filename}" frameborder="0" marginwidth="0" marginheight="0" scrolling="no" width="640" height="360" allowfullscreen></iframe>'
                add_upload_log(upload_id, f"Filemoon upload successful. File ID: {file_id}", "SUCCESS")
                return {'file_id': file_id, 'embed_code': embed_code}
            else:
                raise Exception(f"Upload failed: {result}")

class UpnShareService(VideoHostService):
    """UpnShare upload service"""
    
    async def upload_file(self, file_path: str, filename: str, upload_id: str, progress_callback=None):
        session = await self.get_session()
        headers = {'Authorization': f'Bearer {self.api_key}'}
        url = "https://upnshare.com/api/v1/video/upload"
        
        add_upload_log(upload_id, f"Starting UpnShare upload")
        
        data = aiohttp.FormData()
        async with aiofiles.open(file_path, 'rb') as f:
            file_content = await f.read()
            data.add_field('file', file_content, filename=filename)
        
        async with session.post(url, data=data, headers=headers) as response:
            result = await response.json()
            if result.get('success'):
                video_id = result.get('video_id', 'unknown')
                embed_code = f'<iframe src="https://streamdb.upns.online/#{video_id}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>'
                add_upload_log(upload_id, f"UpnShare upload successful. Video ID: {video_id}", "SUCCESS")
                return {'video_id': video_id, 'embed_code': embed_code}
            else:
                raise Exception(f"Upload failed: {result}")

# Initialize services
services = {
    'lulustream': LulustreamService(LULUSTREAM_API_KEY) if LULUSTREAM_API_KEY else None,
    'streamp2p': StreamP2PService(STREAMP2P_API_KEY) if STREAMP2P_API_KEY else None,
    'rpmshare': RPMShareService(RPMSHARE_API_KEY) if RPMSHARE_API_KEY else None,
    'filemoon': FilemoonService(FILEMOON_API_KEY) if FILEMOON_API_KEY else None,
    'upnshare': UpnShareService(UPNSHARE_API_KEY) if UPNSHARE_API_KEY else None
}

async def upload_to_service(service_name: str, service: VideoHostService, file_path: str, filename: str, upload_id: str):
    """Upload file to a specific service"""
    try:
        # Check if upload was cancelled
        if upload_id in active_uploads and active_uploads[upload_id].get('status') == 'cancelled':
            add_upload_log(upload_id, f"{service_name} upload cancelled", "WARNING")
            return {service_name: {'cancelled': True}}
        
        result = await service.upload_file(file_path, filename, upload_id)
        
        # Update upload results in storage
        with storage_lock:
            if upload_id in uploads_storage:
                if 'results' not in uploads_storage[upload_id]:
                    uploads_storage[upload_id]['results'] = {}
                uploads_storage[upload_id]['results'][service_name] = result
        
        return {service_name: result}
    except Exception as e:
        error_msg = str(e)
        add_upload_log(upload_id, f"Upload to {service_name} failed: {error_msg}", "ERROR")
        
        # Update error in storage
        with storage_lock:
            if upload_id in uploads_storage:
                if 'errors' not in uploads_storage[upload_id]:
                    uploads_storage[upload_id]['errors'] = {}
                uploads_storage[upload_id]['errors'][service_name] = error_msg
        
        return {service_name: {'error': error_msg}}

async def upload_to_all_services(file_path: str, filename: str, upload_id: str):
    """Upload file to all configured services concurrently"""
    async with upload_semaphore:
        active_services = {name: service for name, service in services.items() if service and service.api_key}
        
        if not active_services:
            raise Exception("No services configured. Please check your API keys in .env file.")
        
        add_upload_log(upload_id, f"Starting upload to {len(active_services)} services: {', '.join(active_services.keys())}")
        
        # Create upload tasks for all services
        tasks = [
            upload_to_service(name, service, file_path, filename, upload_id)
            for name, service in active_services.items()
        ]
        
        # Execute all uploads concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Combine results
        combined_results = {}
        for result in results:
            if isinstance(result, dict):
                combined_results.update(result)
            else:
                add_upload_log(upload_id, f"Upload error: {result}", "ERROR")
        
        return combined_results

@app.post("/api/upload")
async def upload_video(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    title: Optional[str] = Form(None)
):
    """Upload video file to all configured services"""
    
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    # Validate file size and type
    file_size = 0
    temp_file = None
    
    try:
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False)
        
        # Read and save file in chunks to prevent memory issues
        async with aiofiles.open(temp_file.name, 'wb') as f:
            while chunk := await file.read(8192):  # 8KB chunks
                await f.write(chunk)
                file_size += len(chunk)
                
                # Check if file is getting too large during upload
                if file_size > 15 * 1024 * 1024 * 1024:  # 15GB absolute max
                    os.unlink(temp_file.name)
                    raise HTTPException(status_code=400, detail="File too large (max 15GB)")
        
        # Validate file size
        if file_size < 100:  # 100 bytes min
            os.unlink(temp_file.name)
            raise HTTPException(status_code=400, detail="File too small (min 100 bytes)")
        
        # Generate upload ID
        upload_id = str(uuid.uuid4())
        
        add_system_log(f"New upload started: {file.filename} ({file_size} bytes)")
        add_upload_log(upload_id, f"Upload initiated for {file.filename} ({file_size} bytes)")
        
        # Store initial upload record in memory
        upload_record = {
            'upload_id': upload_id,
            'filename': file.filename,
            'title': title or file.filename.rsplit('.', 1)[0],
            'file_size': file_size,
            'status': 'uploading',
            'created_at': datetime.utcnow().isoformat(),
            'results': {},
            'errors': {}
        }
        
        with storage_lock:
            uploads_storage[upload_id] = upload_record
        
        # Add to active uploads
        active_uploads[upload_id] = {
            'filename': file.filename,
            'status': 'uploading',
            'progress': 0,
            'temp_file': temp_file.name
        }
        
        # Start background upload task
        task = background_tasks.add_task(
            process_upload,
            temp_file.name,
            file.filename,
            upload_id
        )
        
        # Store task for potential cancellation
        task_registry[upload_id] = task
        
        return JSONResponse({
            'success': True,
            'upload_id': upload_id,
            'filename': file.filename,
            'file_size': file_size,
            'message': 'Upload started successfully'
        })
    
    except HTTPException:
        if temp_file and os.path.exists(temp_file.name):
            os.unlink(temp_file.name)
        raise
    except Exception as e:
        if temp_file and os.path.exists(temp_file.name):
            os.unlink(temp_file.name)
        error_msg = str(e) if str(e) else f"Unknown error: {type(e).__name__}"
        add_system_log(f"Upload failed: {error_msg}", "ERROR")
        raise HTTPException(status_code=500, detail=f"Upload failed: {error_msg}")

async def process_upload(file_path: str, filename: str, upload_id: str):
    """Background task to process upload to all services"""
    try:
        # Update status
        active_uploads[upload_id]['status'] = 'processing'
        add_upload_log(upload_id, "Starting upload processing")
        
        # Upload to all services
        results = await upload_to_all_services(file_path, filename, upload_id)
        
        # Check if upload was cancelled
        if active_uploads[upload_id].get('status') == 'cancelled':
            add_upload_log(upload_id, "Upload cancelled by user", "WARNING")
            return
        
        # Update final status
        active_uploads[upload_id]['status'] = 'completed'
        active_uploads[upload_id]['progress'] = 100
        
        with storage_lock:
            if upload_id in uploads_storage:
                uploads_storage[upload_id]['status'] = 'completed'
                uploads_storage[upload_id]['completed_at'] = datetime.utcnow().isoformat()
                uploads_storage[upload_id]['final_results'] = results
        
        add_upload_log(upload_id, "Upload completed successfully", "SUCCESS")
        
        # Generate CSV if all uploads successful
        await generate_embed_codes_csv(upload_id, filename, results)
        
    except Exception as e:
        error_msg = str(e)
        add_upload_log(upload_id, f"Upload processing failed: {error_msg}", "ERROR")
        add_system_log(f"Upload processing failed for {filename}: {error_msg}", "ERROR")
        
        active_uploads[upload_id]['status'] = 'failed'
        active_uploads[upload_id]['error'] = error_msg
        
        with storage_lock:
            if upload_id in uploads_storage:
                uploads_storage[upload_id]['status'] = 'failed'
                uploads_storage[upload_id]['error'] = error_msg
                uploads_storage[upload_id]['failed_at'] = datetime.utcnow().isoformat()
    finally:
        # Cleanup temporary file
        if os.path.exists(file_path):
            os.unlink(file_path)
        
        # Remove from task registry
        if upload_id in task_registry:
            del task_registry[upload_id]

async def generate_embed_codes_csv(upload_id: str, filename: str, results: Dict):
    """Generate CSV file with embed codes in the specified order"""
    try:
        # Order as specified by user: Lulustream, Streamp2p, RPMShare, Filemoon, UpnShare
        service_order = ['lulustream', 'streamp2p', 'rpmshare', 'filemoon', 'upnshare']
        
        embed_codes = []
        for service in service_order:
            if service in results and 'embed_code' in results[service]:
                embed_codes.append(results[service]['embed_code'])
        
        if embed_codes:
            # Create CSV content with specified format
            csv_content = f"{filename}," + "\n\n".join(embed_codes)
            
            # Save CSV file
            csv_filename = f"embed_codes_{upload_id}.csv"
            csv_path = f"/tmp/{csv_filename}"
            
            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                csvfile.write(csv_content)
            
            # Store CSV path in storage
            with storage_lock:
                if upload_id in uploads_storage:
                    uploads_storage[upload_id]['csv_file'] = csv_path
                    uploads_storage[upload_id]['csv_filename'] = csv_filename
            
            add_upload_log(upload_id, f"CSV file generated: {csv_filename}", "SUCCESS")
    
    except Exception as e:
        add_upload_log(upload_id, f"Failed to generate CSV: {str(e)}", "ERROR")

@app.get("/api/status/{upload_id}")
async def get_upload_status(upload_id: str):
    """Get upload status and progress"""
    
    # Check active uploads first
    if upload_id in active_uploads:
        return JSONResponse(active_uploads[upload_id])
    
    # Check storage
    with storage_lock:
        if upload_id in uploads_storage:
            upload_record = uploads_storage[upload_id].copy()
            upload_record['csv_available'] = 'csv_file' in upload_record
            return JSONResponse(upload_record)
    
    raise HTTPException(status_code=404, detail="Upload not found")

@app.get("/api/uploads")
async def get_all_uploads():
    """Get all upload records"""
    
    with storage_lock:
        uploads = []
        for upload_id, upload_record in uploads_storage.items():
            record_copy = upload_record.copy()
            record_copy['upload_id'] = upload_id
            record_copy['csv_available'] = 'csv_file' in record_copy
            uploads.append(record_copy)
        
        # Sort by creation time (newest first)
        uploads.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        
        return JSONResponse({'uploads': uploads[-50:]})  # Return last 50 uploads

@app.get("/api/logs/{upload_id}")
async def get_upload_logs(upload_id: str):
    """Get logs for specific upload"""
    
    with log_lock:
        if upload_id in upload_logs:
            return JSONResponse({
                'upload_id': upload_id,
                'logs': upload_logs[upload_id]
            })
    
    raise HTTPException(status_code=404, detail="Upload logs not found")

@app.get("/api/system-logs")
async def get_system_logs():
    """Get system logs"""
    
    with log_lock:
        return JSONResponse({'logs': system_logs[-100:]})  # Return last 100 system logs

@app.post("/api/cancel/{upload_id}")
async def cancel_upload(upload_id: str):
    """Cancel active upload"""
    
    try:
        if upload_id in active_uploads:
            # Mark as cancelled
            active_uploads[upload_id]['status'] = 'cancelled'
            
            # Update storage
            with storage_lock:
                if upload_id in uploads_storage:
                    uploads_storage[upload_id]['status'] = 'cancelled'
                    uploads_storage[upload_id]['cancelled_at'] = datetime.utcnow().isoformat()
            
            # Clean up temp file if exists
            if 'temp_file' in active_uploads[upload_id]:
                temp_file = active_uploads[upload_id]['temp_file']
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
            
            add_upload_log(upload_id, "Upload cancelled by user", "WARNING")
            add_system_log(f"Upload cancelled: {upload_id}")
            
            return JSONResponse({'success': True, 'message': 'Upload cancelled successfully'})
        
        raise HTTPException(status_code=404, detail="Upload not found or not active")
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to cancel upload: {str(e)}")

@app.post("/api/clear-all")
async def clear_all_data():
    """Clear all queues, logs, and upload data"""
    
    try:
        # Cancel all active uploads first
        for upload_id in list(active_uploads.keys()):
            if 'temp_file' in active_uploads[upload_id]:
                temp_file = active_uploads[upload_id]['temp_file']
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
        
        # Clear all data
        with storage_lock:
            uploads_storage.clear()
        
        with log_lock:
            upload_logs.clear()
            system_logs.clear()
        
        active_uploads.clear()
        upload_queue.clear()
        task_registry.clear()
        
        # Clean up any CSV files
        csv_files = [f for f in os.listdir('/tmp') if f.startswith('embed_codes_')]
        for csv_file in csv_files:
            try:
                os.unlink(f'/tmp/{csv_file}')
            except:
                pass
        
        add_system_log("All data cleared successfully")
        
        return JSONResponse({'success': True, 'message': 'All data cleared successfully'})
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to clear data: {str(e)}")

@app.get("/api/download-csv/{upload_id}")
async def download_csv(upload_id: str):
    """Download generated CSV file with embed codes"""
    
    with storage_lock:
        if upload_id in uploads_storage and 'csv_file' in uploads_storage[upload_id]:
            csv_path = uploads_storage[upload_id]['csv_file']
            csv_filename = uploads_storage[upload_id]['csv_filename']
            
            if os.path.exists(csv_path):
                return FileResponse(
                    csv_path,
                    filename=csv_filename,
                    media_type='text/csv'
                )
    
    raise HTTPException(status_code=404, detail="CSV file not found")

@app.get("/api/system-status")
async def get_system_status():
    """Get system resource usage and upload queue status"""
    
    # Get system resources
    cpu_percent = psutil.cpu_percent(interval=0.1)  # Reduced interval
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    # Get active uploads count
    active_count = len(active_uploads)
    
    # Get network I/O
    net_io = psutil.net_io_counters()
    
    # Count configured services
    configured_services = sum([
        1 if LULUSTREAM_API_KEY else 0,
        1 if STREAMP2P_API_KEY else 0,
        1 if RPMSHARE_API_KEY else 0,
        1 if FILEMOON_API_KEY else 0,
        1 if UPNSHARE_API_KEY else 0
    ])
    
    return JSONResponse({
        'status': 'healthy',
        'cpu_usage': cpu_percent,
        'memory_usage': memory.percent,
        'memory_available': memory.available,
        'disk_usage': disk.percent,
        'disk_free': disk.free,
        'active_uploads': active_count,
        'max_concurrent': MAX_CONCURRENT_UPLOADS,
        'bandwidth_limit': BANDWIDTH_LIMIT_PERCENT,
        'services_configured': configured_services,
        'total_uploads': len(uploads_storage),
        'system_logs_count': len(system_logs),
        'services': {
            'lulustream': {
                'configured': bool(LULUSTREAM_API_KEY), 
                'status': 'ready' if LULUSTREAM_API_KEY else 'not_configured',
                'active_uploads': len([p for p in service_progress.values() if p.get('service') == 'lulustream']),
                'progress': service_progress.get('lulustream', {})
            },
            'streamp2p': {
                'configured': bool(STREAMP2P_API_KEY), 
                'status': 'ready' if STREAMP2P_API_KEY else 'not_configured',
                'active_uploads': len([p for p in service_progress.values() if p.get('service') == 'streamp2p']),
                'progress': service_progress.get('streamp2p', {})
            },
            'rpmshare': {
                'configured': bool(RPMSHARE_API_KEY), 
                'status': 'ready' if RPMSHARE_API_KEY else 'not_configured',
                'active_uploads': len([p for p in service_progress.values() if p.get('service') == 'rpmshare']),
                'progress': service_progress.get('rpmshare', {})
            },
            'filemoon': {
                'configured': bool(FILEMOON_API_KEY), 
                'status': 'ready' if FILEMOON_API_KEY else 'not_configured',
                'active_uploads': len([p for p in service_progress.values() if p.get('service') == 'filemoon']),
                'progress': service_progress.get('filemoon', {})
            },
            'upnshare': {
                'configured': bool(UPNSHARE_API_KEY), 
                'status': 'ready' if UPNSHARE_API_KEY else 'not_configured',
                'active_uploads': len([p for p in service_progress.values() if p.get('service') == 'upnshare']),
                'progress': service_progress.get('upnshare', {})
            }
        }
    })

@app.get("/api/temp-folder/{upload_id}")
async def get_temp_folder(upload_id: str):
    """Get temporary folder path for an upload"""
    try:
        with storage_lock:
            if upload_id in uploads_storage:
                upload_data = uploads_storage[upload_id]
                if 'temp_file' in upload_data:
                    temp_path = os.path.dirname(upload_data['temp_file'])
                    return JSONResponse({
                        'temp_path': temp_path,
                        'temp_file': upload_data['temp_file']
                    })
        
        return JSONResponse({
            'error': 'Upload not found or no temp file'
        }, status_code=404)
    except Exception as e:
        add_system_log(f"Error getting temp folder: {str(e)}", "ERROR")
        return JSONResponse({
            'error': str(e)
        }, status_code=500)

@app.delete("/api/uploads/{upload_id}")
async def delete_upload(upload_id: str):
    """Delete upload record and associated files"""
    
    try:
        # Cancel if active
        if upload_id in active_uploads:
            await cancel_upload(upload_id)
        
        # Remove from storage
        with storage_lock:
            if upload_id in uploads_storage:
                upload_record = uploads_storage[upload_id]
                
                # Delete CSV file if exists
                if 'csv_file' in upload_record and os.path.exists(upload_record['csv_file']):
                    os.unlink(upload_record['csv_file'])
                
                # Remove from storage
                del uploads_storage[upload_id]
        
        # Remove logs
        with log_lock:
            if upload_id in upload_logs:
                del upload_logs[upload_id]
        
        # Remove from active uploads
        if upload_id in active_uploads:
            del active_uploads[upload_id]
        
        add_system_log(f"Upload deleted: {upload_id}")
        
        return JSONResponse({'success': True, 'message': 'Upload deleted successfully'})
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete upload: {str(e)}")

@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    configured_services = len([s for s in services.values() if s and s.api_key])
    add_system_log("Video Upload Automation API started")
    add_system_log(f"Services configured: {configured_services}/5")
    add_system_log(f"Max concurrent uploads: {MAX_CONCURRENT_UPLOADS}")
    add_system_log(f"Bandwidth limit: {BANDWIDTH_LIMIT_PERCENT}%")

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    add_system_log("Shutting down services...")
    
    # Close all service sessions
    for service in services.values():
        if service:
            await service.close_session()
    
    # Clean up temporary files
    for upload_id, upload_data in active_uploads.items():
        if 'temp_file' in upload_data and os.path.exists(upload_data['temp_file']):
            os.unlink(upload_data['temp_file'])

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)