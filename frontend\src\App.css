/* Tailwind CSS base styles are imported through index.css */

.App {
  text-align: center;
}

/* Custom animations for upload progress */
.upload-progress {
  position: relative;
  overflow: hidden;
}

.upload-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Drag and drop styles */
.drag-active {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

/* File upload animations */
.file-enter {
  opacity: 0;
  transform: translateY(20px);
}

.file-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

.file-exit {
  opacity: 1;
}

.file-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms, transform 300ms;
}

/* Status indicators */
.status-indicator {
  position: relative;
  display: inline-block;
}

.status-indicator.pulsing::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  background-color: inherit;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Service grid animations */
.service-grid {
  display: grid;
  gap: 0.5rem;
  grid-template-columns: repeat(5, 1fr);
}

.service-status {
  padding: 0.5rem;
  border-radius: 0.375rem;
  text-align: center;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s;
}

.service-status.success {
  background-color: #dcfce7;
  color: #166534;
}

.service-status.error {
  background-color: #fecaca;
  color: #991b1b;
}

.service-status.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.service-status.uploading {
  background-color: #dbeafe;
  color: #1e40af;
}

/* Responsive design */
@media (max-width: 768px) {
  .service-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 640px) {
  .service-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Upload queue styles */
.upload-queue {
  max-height: 300px;
  overflow-y: auto;
}

.upload-queue::-webkit-scrollbar {
  width: 6px;
}

.upload-queue::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.upload-queue::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.upload-queue::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Progress bar styles */
.progress-bar {
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* Bandwidth indicator */
.bandwidth-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.bandwidth-bar {
  width: 100px;
  height: 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.bandwidth-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* System status cards */
.status-card {
  background: white;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.status-card h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.status-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
}

.status-label {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Embed code preview */
.embed-preview {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  padding: 0.75rem;
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.75rem;
  color: #475569;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 120px;
  overflow-y: auto;
}

/* Notification styles */
.notification {
  position: fixed;
  top: 1rem;
  right: 1rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #10b981;
  padding: 1rem;
  max-width: 400px;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

.notification.error {
  border-left-color: #ef4444;
}

.notification.warning {
  border-left-color: #f59e0b;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Loading spinner */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Button loading state */
.button-loading {
  position: relative;
  color: transparent;
}

.button-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin-top: -8px;
  margin-left: -8px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s, box-shadow 0.2s;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Focus styles for accessibility */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* Dark mode styles (optional) */
@media (prefers-color-scheme: dark) {
  .embed-preview {
    background-color: #1f2937;
    border-color: #374151;
    color: #d1d5db;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .status-indicator {
    border: 2px solid;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .upload-progress::after,
  .spinner,
  .button-loading::after {
    animation: none;
  }
  
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
  }
}