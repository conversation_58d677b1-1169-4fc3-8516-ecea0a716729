#!/usr/bin/env python3
"""
Comprehensive Backend Testing Suite for Video Upload Automation API
Tests all FastAPI endpoints and core functionality
"""

import requests
import json
import time
import os
import tempfile
from typing import Dict, Any
import uuid

# Get backend URL from environment
BACKEND_URL = "https://2e471a81-ee35-4ad4-bc1b-90cf5933aa09.preview.emergentagent.com"
API_BASE = f"{BACKEND_URL}/api"

class VideoUploadAPITester:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = {}
        self.upload_ids = []
        
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test results"""
        self.test_results[test_name] = {
            'success': success,
            'details': details
        }
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}")
        if details:
            print(f"    Details: {details}")
    
    def test_system_status(self):
        """Test /api/system-status endpoint"""
        try:
            response = self.session.get(f"{API_BASE}/system-status", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                required_fields = ['cpu_usage', 'memory_usage', 'active_uploads', 'services_configured']
                
                missing_fields = [field for field in required_fields if field not in data]
                if missing_fields:
                    self.log_test("System Status API", False, f"Missing fields: {missing_fields}")
                    return False
                
                # Validate data types
                if not isinstance(data['cpu_usage'], (int, float)):
                    self.log_test("System Status API", False, "CPU usage should be numeric")
                    return False
                
                self.log_test("System Status API", True, f"Services configured: {data.get('services_configured', 0)}")
                return True
            else:
                self.log_test("System Status API", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("System Status API", False, f"Exception: {str(e)}")
            return False
    
    def test_get_uploads(self):
        """Test /api/uploads endpoint"""
        try:
            response = self.session.get(f"{API_BASE}/uploads", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'uploads' not in data:
                    self.log_test("Get Uploads API", False, "Missing 'uploads' field in response")
                    return False
                
                if not isinstance(data['uploads'], list):
                    self.log_test("Get Uploads API", False, "Uploads should be a list")
                    return False
                
                self.log_test("Get Uploads API", True, f"Found {len(data['uploads'])} uploads")
                return True
            else:
                self.log_test("Get Uploads API", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("Get Uploads API", False, f"Exception: {str(e)}")
            return False
    
    def create_test_video_file(self, size_mb: float = 0.1):
        """Create a small test video file for upload testing"""
        try:
            # Create a temporary file with video-like content
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp4')
            
            # Write some binary data to simulate a video file
            test_data = b'\x00\x00\x00\x20ftypmp42\x00\x00\x00\x00mp42isom' + b'\x00' * int(size_mb * 1024 * 1024)
            temp_file.write(test_data)
            temp_file.close()
            
            return temp_file.name
        except Exception as e:
            print(f"Failed to create test file: {e}")
            return None
    
    def test_file_upload(self):
        """Test /api/upload endpoint with a small test file"""
        try:
            # Create test file
            test_file_path = self.create_test_video_file(0.1)  # 100KB test file
            if not test_file_path:
                self.log_test("File Upload API", False, "Could not create test file")
                return False
            
            try:
                with open(test_file_path, 'rb') as f:
                    files = {'file': ('test_video.mp4', f, 'video/mp4')}
                    data = {'title': 'Test Video Upload'}
                    
                    response = self.session.post(f"{API_BASE}/upload", files=files, data=data, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    
                    required_fields = ['success', 'upload_id', 'filename', 'file_size']
                    missing_fields = [field for field in required_fields if field not in result]
                    
                    if missing_fields:
                        self.log_test("File Upload API", False, f"Missing fields: {missing_fields}")
                        return False
                    
                    if not result.get('success'):
                        self.log_test("File Upload API", False, "Upload not marked as successful")
                        return False
                    
                    upload_id = result.get('upload_id')
                    if upload_id:
                        self.upload_ids.append(upload_id)
                    
                    self.log_test("File Upload API", True, f"Upload ID: {upload_id}, Size: {result.get('file_size')} bytes")
                    return True
                else:
                    self.log_test("File Upload API", False, f"HTTP {response.status_code}: {response.text}")
                    return False
                    
            finally:
                # Cleanup test file
                if os.path.exists(test_file_path):
                    os.unlink(test_file_path)
                    
        except Exception as e:
            self.log_test("File Upload API", False, f"Exception: {str(e)}")
            return False
    
    def test_upload_status(self):
        """Test /api/status/{upload_id} endpoint"""
        if not self.upload_ids:
            self.log_test("Upload Status API", False, "No upload IDs available for testing")
            return False
        
        try:
            upload_id = self.upload_ids[0]
            response = self.session.get(f"{API_BASE}/status/{upload_id}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # Check for expected fields
                if 'status' not in data:
                    self.log_test("Upload Status API", False, "Missing 'status' field")
                    return False
                
                status = data.get('status')
                valid_statuses = ['uploading', 'processing', 'completed', 'failed']
                
                if status not in valid_statuses:
                    self.log_test("Upload Status API", False, f"Invalid status: {status}")
                    return False
                
                self.log_test("Upload Status API", True, f"Status: {status}")
                return True
            else:
                self.log_test("Upload Status API", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("Upload Status API", False, f"Exception: {str(e)}")
            return False
    
    def test_invalid_upload_status(self):
        """Test /api/status/{upload_id} with invalid ID"""
        try:
            fake_id = str(uuid.uuid4())
            response = self.session.get(f"{API_BASE}/status/{fake_id}", timeout=10)
            
            if response.status_code == 404:
                self.log_test("Invalid Upload Status API", True, "Correctly returned 404 for invalid ID")
                return True
            else:
                self.log_test("Invalid Upload Status API", False, f"Expected 404, got {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Invalid Upload Status API", False, f"Exception: {str(e)}")
            return False
    
    def test_csv_download(self):
        """Test /api/download-csv/{upload_id} endpoint"""
        if not self.upload_ids:
            self.log_test("CSV Download API", False, "No upload IDs available for testing")
            return False
        
        try:
            upload_id = self.upload_ids[0]
            
            # Wait a bit for upload processing
            time.sleep(2)
            
            response = self.session.get(f"{API_BASE}/download-csv/{upload_id}", timeout=10)
            
            # Since API keys are not configured, CSV might not be generated
            # We expect either 200 (CSV exists) or 404 (CSV not found due to failed uploads)
            if response.status_code == 200:
                if response.headers.get('content-type') == 'text/csv':
                    self.log_test("CSV Download API", True, "CSV file downloaded successfully")
                    return True
                else:
                    self.log_test("CSV Download API", False, "Response not in CSV format")
                    return False
            elif response.status_code == 404:
                self.log_test("CSV Download API", True, "CSV not found (expected due to no API keys)")
                return True
            else:
                self.log_test("CSV Download API", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("CSV Download API", False, f"Exception: {str(e)}")
            return False
    
    def test_invalid_file_upload(self):
        """Test upload with invalid file types and sizes"""
        try:
            # Test with empty file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.txt')
            temp_file.write(b'')  # Empty file
            temp_file.close()
            
            try:
                with open(temp_file.name, 'rb') as f:
                    files = {'file': ('empty.txt', f, 'text/plain')}
                    response = self.session.post(f"{API_BASE}/upload", files=files, timeout=10)
                
                if response.status_code == 400:
                    self.log_test("Invalid File Upload (Empty)", True, "Correctly rejected empty file")
                else:
                    self.log_test("Invalid File Upload (Empty)", False, f"Expected 400, got {response.status_code}")
                    
            finally:
                os.unlink(temp_file.name)
            
            return True
            
        except Exception as e:
            self.log_test("Invalid File Upload", False, f"Exception: {str(e)}")
            return False
    
    def test_in_memory_storage(self):
        """Test in-memory storage functionality (replacing MongoDB)"""
        try:
            # Test system status which should show storage info
            response = self.session.get(f"{API_BASE}/system-status", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                # Check that we have total_uploads field (indicates storage is working)
                if 'total_uploads' in data:
                    self.log_test("In-Memory Storage", True, f"Storage working - Total uploads: {data['total_uploads']}")
                    return True
                else:
                    self.log_test("In-Memory Storage", False, "Missing total_uploads field in system status")
                    return False
            else:
                self.log_test("In-Memory Storage", False, "System status endpoint failed")
                return False
                
        except Exception as e:
            self.log_test("In-Memory Storage", False, f"Exception: {str(e)}")
            return False
    
    def test_cancel_upload(self):
        """Test /api/cancel/{upload_id} endpoint"""
        try:
            # First create an upload to cancel
            test_file_path = self.create_test_video_file(0.1)
            if not test_file_path:
                self.log_test("Cancel Upload API", False, "Could not create test file")
                return False
            
            upload_id = None
            try:
                with open(test_file_path, 'rb') as f:
                    files = {'file': ('cancel_test.mp4', f, 'video/mp4')}
                    data = {'title': 'Cancel Test Video'}
                    
                    response = self.session.post(f"{API_BASE}/upload", files=files, data=data, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    upload_id = result.get('upload_id')
                    
                    if upload_id:
                        # Now try to cancel it
                        cancel_response = self.session.post(f"{API_BASE}/cancel/{upload_id}", timeout=10)
                        
                        if cancel_response.status_code == 200:
                            cancel_result = cancel_response.json()
                            if cancel_result.get('success'):
                                self.log_test("Cancel Upload API", True, f"Successfully cancelled upload {upload_id}")
                                return True
                            else:
                                self.log_test("Cancel Upload API", False, "Cancel response not marked as successful")
                                return False
                        else:
                            self.log_test("Cancel Upload API", False, f"Cancel failed with HTTP {cancel_response.status_code}")
                            return False
                    else:
                        self.log_test("Cancel Upload API", False, "No upload ID returned from upload")
                        return False
                else:
                    self.log_test("Cancel Upload API", False, f"Upload failed with HTTP {response.status_code}")
                    return False
                    
            finally:
                if os.path.exists(test_file_path):
                    os.unlink(test_file_path)
                    
        except Exception as e:
            self.log_test("Cancel Upload API", False, f"Exception: {str(e)}")
            return False
    
    def test_cancel_invalid_upload(self):
        """Test /api/cancel/{upload_id} with invalid upload ID"""
        try:
            fake_id = str(uuid.uuid4())
            response = self.session.post(f"{API_BASE}/cancel/{fake_id}", timeout=10)
            
            if response.status_code == 404:
                self.log_test("Cancel Invalid Upload API", True, "Correctly returned 404 for invalid upload ID")
                return True
            else:
                self.log_test("Cancel Invalid Upload API", False, f"Expected 404, got {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("Cancel Invalid Upload API", False, f"Exception: {str(e)}")
            return False
    
    def test_upload_logs(self):
        """Test /api/logs/{upload_id} endpoint"""
        if not self.upload_ids:
            self.log_test("Upload Logs API", False, "No upload IDs available for testing")
            return False
        
        try:
            upload_id = self.upload_ids[0]
            response = self.session.get(f"{API_BASE}/logs/{upload_id}", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                required_fields = ['upload_id', 'logs']
                missing_fields = [field for field in required_fields if field not in data]
                
                if missing_fields:
                    self.log_test("Upload Logs API", False, f"Missing fields: {missing_fields}")
                    return False
                
                if not isinstance(data['logs'], list):
                    self.log_test("Upload Logs API", False, "Logs should be a list")
                    return False
                
                self.log_test("Upload Logs API", True, f"Retrieved {len(data['logs'])} log entries")
                return True
            elif response.status_code == 404:
                self.log_test("Upload Logs API", True, "No logs found (expected for some uploads)")
                return True
            else:
                self.log_test("Upload Logs API", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("Upload Logs API", False, f"Exception: {str(e)}")
            return False
    
    def test_system_logs(self):
        """Test /api/system-logs endpoint"""
        try:
            response = self.session.get(f"{API_BASE}/system-logs", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'logs' not in data:
                    self.log_test("System Logs API", False, "Missing 'logs' field in response")
                    return False
                
                if not isinstance(data['logs'], list):
                    self.log_test("System Logs API", False, "Logs should be a list")
                    return False
                
                # Check log entry structure if logs exist
                if data['logs']:
                    log_entry = data['logs'][0]
                    required_log_fields = ['timestamp', 'level', 'message']
                    missing_log_fields = [field for field in required_log_fields if field not in log_entry]
                    
                    if missing_log_fields:
                        self.log_test("System Logs API", False, f"Log entry missing fields: {missing_log_fields}")
                        return False
                
                self.log_test("System Logs API", True, f"Retrieved {len(data['logs'])} system log entries")
                return True
            else:
                self.log_test("System Logs API", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("System Logs API", False, f"Exception: {str(e)}")
            return False
    
    def test_clear_all(self):
        """Test /api/clear-all endpoint"""
        try:
            # First check current uploads count
            uploads_response = self.session.get(f"{API_BASE}/uploads", timeout=10)
            initial_count = 0
            if uploads_response.status_code == 200:
                initial_data = uploads_response.json()
                initial_count = len(initial_data.get('uploads', []))
            
            # Now clear all data
            response = self.session.post(f"{API_BASE}/clear-all", timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                
                if not result.get('success'):
                    self.log_test("Clear All API", False, "Clear all not marked as successful")
                    return False
                
                # Verify data was cleared by checking uploads
                time.sleep(1)  # Give it a moment
                verify_response = self.session.get(f"{API_BASE}/uploads", timeout=10)
                
                if verify_response.status_code == 200:
                    verify_data = verify_response.json()
                    remaining_count = len(verify_data.get('uploads', []))
                    
                    if remaining_count == 0:
                        self.log_test("Clear All API", True, f"Successfully cleared all data (was {initial_count} uploads)")
                        # Clear our local upload_ids since they're now invalid
                        self.upload_ids.clear()
                        return True
                    else:
                        self.log_test("Clear All API", False, f"Still {remaining_count} uploads remaining after clear")
                        return False
                else:
                    self.log_test("Clear All API", False, "Could not verify clear operation")
                    return False
            else:
                self.log_test("Clear All API", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("Clear All API", False, f"Exception: {str(e)}")
            return False
    
    def test_performance_limits(self):
        """Test that performance limits are properly configured"""
        try:
            response = self.session.get(f"{API_BASE}/system-status", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                # Check max concurrent uploads (should be 3 for local use)
                max_concurrent = data.get('max_concurrent', 0)
                if max_concurrent != 3:
                    self.log_test("Performance Limits", False, f"Expected max_concurrent=3, got {max_concurrent}")
                    return False
                
                # Check bandwidth limit (should be 70% for local use)
                bandwidth_limit = data.get('bandwidth_limit', 0)
                if bandwidth_limit != 70:
                    self.log_test("Performance Limits", False, f"Expected bandwidth_limit=70, got {bandwidth_limit}")
                    return False
                
                self.log_test("Performance Limits", True, f"Concurrent: {max_concurrent}, Bandwidth: {bandwidth_limit}%")
                return True
            else:
                self.log_test("Performance Limits", False, f"HTTP {response.status_code}: {response.text}")
                return False
                
        except Exception as e:
            self.log_test("Performance Limits", False, f"Exception: {str(e)}")
            return False
    
    def run_all_tests(self):
        """Run all backend tests"""
        print("=" * 60)
        print("STARTING COMPREHENSIVE BACKEND API TESTING")
        print("=" * 60)
        print(f"Backend URL: {BACKEND_URL}")
        print(f"API Base: {API_BASE}")
        print()
        
        # Core API endpoint tests
        print("Testing Core API Endpoints...")
        self.test_system_status()
        self.test_get_uploads()
        
        # Database connectivity
        print("\nTesting Database Integration...")
        self.test_database_connectivity()
        
        # File upload functionality
        print("\nTesting File Upload Functionality...")
        self.test_file_upload()
        self.test_upload_status()
        self.test_csv_download()
        
        # Error handling tests
        print("\nTesting Error Handling...")
        self.test_invalid_file_upload()
        self.test_invalid_upload_status()
        
        # Concurrent upload handling
        print("\nTesting Concurrent Upload Management...")
        self.test_concurrent_upload_handling()
        
        # Summary
        print("\n" + "=" * 60)
        print("TEST RESULTS SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\nFAILED TESTS:")
            for test_name, result in self.test_results.items():
                if not result['success']:
                    print(f"  ❌ {test_name}: {result['details']}")
        
        print(f"\nGenerated Upload IDs for further testing: {len(self.upload_ids)}")
        
        return {
            'total': total_tests,
            'passed': passed_tests,
            'failed': failed_tests,
            'success_rate': (passed_tests/total_tests)*100,
            'upload_ids': self.upload_ids,
            'details': self.test_results
        }

if __name__ == "__main__":
    tester = VideoUploadAPITester()
    results = tester.run_all_tests()
    
    # Exit with appropriate code
    exit(0 if results['failed'] == 0 else 1)