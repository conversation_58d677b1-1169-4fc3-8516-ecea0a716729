{"version": 3, "file": "static/css/main.21b94717.css", "mappings": "AAAA,wCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd,kCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,gHAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAEd,qBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,uBAAmB,CAAnB,kCAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,+BAAmB,CAAnB,8BAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,+BAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,gCAAmB,CAAnB,yBAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,iCAAmB,CAAnB,8BAAmB,CAAnB,kCAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,kDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,8CAAmB,CAAnB,4CAAmB,CAAnB,0CAAmB,CAAnB,0CAAmB,CAAnB,8BAAmB,CAAnB,8GAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,4CAAmB,CAAnB,sDAAmB,CAAnB,+CAAmB,CAAnB,kGAAmB,CAAnB,wLAAmB,CAAnB,gEAAmB,CAAnB,kDAAmB,CAAnB,6IAAmB,CAAnB,yFAAmB,CAAnB,uHAAmB,CAAnB,kDAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAEnB,KAKI,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEgC,CAHhC,QAMJ,CAEA,KACI,uEAEJ,CAhBA,mDAiBA,CAjBA,oBAiBA,CAjBA,wDAiBA,CAjBA,2CAiBA,CAjBA,wBAiBA,CAjBA,sDAiBA,CAjBA,2CAiBA,CAjBA,wBAiBA,CAjBA,sDAiBA,CAjBA,2CAiBA,CAjBA,wBAiBA,CAjBA,wDAiBA,CAjBA,2CAiBA,CAjBA,wBAiBA,CAjBA,wDAiBA,CAjBA,2CAiBA,CAjBA,wBAiBA,CAjBA,qDAiBA,CAjBA,2CAiBA,CAjBA,wBAiBA,CAjBA,qDAiBA,CAjBA,4CAiBA,CAjBA,wBAiBA,CAjBA,sDAiBA,CAjBA,0CAiBA,CAjBA,wBAiBA,CAjBA,sDAiBA,CAjBA,6CAiBA,CAjBA,wBAiBA,CAjBA,qDAiBA,CAjBA,+CAiBA,CAjBA,aAiBA,CAjBA,+CAiBA,CAjBA,+CAiBA,CAjBA,aAiBA,CAjBA,6CAiBA,CAjBA,+CAiBA,CAjBA,aAiBA,CAjBA,+CAiBA,CAjBA,+CAiBA,CAjBA,aAiBA,CAjBA,4CAiBA,CAjBA,8CAiBA,CAjBA,aAiBA,CAjBA,+CAiBA,CAjBA,8CAiBA,CAjBA,aAiBA,CAjBA,6CAiBA,CAjBA,yDAiBA,CAjBA,yCAiBA,CAjBA,sDAiBA,CAjBA,oBAiBA,EAjBA,wFAiBA,CAjBA,2BAiBA,CAjBA,kBAiBA,ECfA,KACE,iBACF,CAGA,iBAEE,eAAgB,CADhB,iBAEF,CAEA,uBAOE,+BAAgC,CADhC,mDAAmF,CALnF,UAAW,CAIX,WAAY,CADZ,UAAW,CAFX,iBAAkB,CAClB,KAKF,CAEA,mBACE,GAAK,UAAa,CAClB,GAAO,SAAY,CACrB,CAGA,aAEE,wBAAyB,CADzB,oBAEF,CAGA,YACE,SAAU,CACV,0BACF,CAEA,mBACE,SAAU,CACV,uBAAwB,CACxB,oCACF,CAEA,WACE,SACF,CAEA,kBACE,SAAU,CACV,2BAA4B,CAC5B,oCACF,CAGA,kBAEE,oBAAqB,CADrB,iBAEF,CAEA,gCAQE,mDAAyD,CACzD,wBAAyB,CAFzB,qBAAsB,CADtB,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAOF,CAEA,iBACE,MACE,SACF,CACA,IACE,UACF,CACF,CAGA,cAEE,cAAW,CADX,YAAa,CACb,SAAW,CACX,mCACF,CAEA,gBAEE,qBAAuB,CAEvB,gBAAkB,CAClB,eAAgB,CAJhB,aAAe,CAEf,iBAAkB,CAGlB,kBACF,CAEA,wBACE,wBAAyB,CACzB,aACF,CAEA,sBACE,wBAAyB,CACzB,aACF,CAEA,wBACE,wBAAyB,CACzB,aACF,CAEA,0BACE,wBAAyB,CACzB,aACF,CAGA,yBACE,cACE,mCACF,CACF,CAEA,yBACE,cACE,mCACF,CACF,CAGA,cACE,gBAAiB,CACjB,eACF,CAEA,iCACE,SACF,CAEA,uCACE,kBAAmB,CACnB,iBACF,CAEA,uCACE,kBAAmB,CACnB,iBACF,CAEA,6CACE,kBACF,CAGA,cAEE,wBAAyB,CACzB,iBAAkB,CAFlB,UAAW,CAGX,eACF,CAEA,eAEE,iDAAoD,CACpD,iBAAkB,CAFlB,WAAY,CAGZ,yBACF,CAGA,qBAEE,kBAAmB,CADnB,YAAa,CAGb,iBAAmB,CADnB,SAEF,CAEA,eAGE,wBAAyB,CACzB,iBAAkB,CAFlB,UAAW,CAGX,eAAgB,CAJhB,WAKF,CAEA,gBAEE,iDAAoD,CACpD,iBAAkB,CAFlB,WAAY,CAGZ,yBACF,CAGA,aACE,eAAiB,CAIjB,wBAAyB,CAHzB,mBAAqB,CAErB,gCAA0C,CAD1C,YAGF,CAEA,gBAGE,aAAc,CAFd,iBAAmB,CACnB,eAAgB,CAEhB,mBACF,CAEA,cAGE,aAAc,CAFd,gBAAiB,CACjB,eAEF,CAEA,cAEE,aAAc,CADd,gBAEF,CAGA,eACE,wBAAyB,CACzB,wBAAyB,CACzB,qBAAuB,CAIvB,aAAc,CAFd,qCAA4C,CAC5C,gBAAkB,CAIlB,gBAAiB,CACjB,eAAgB,CAPhB,cAAgB,CAIhB,oBAAqB,CACrB,oBAGF,CAGA,cAWE,8BAAgC,CAPhC,eAAiB,CAGjB,6BAA8B,CAF9B,mBAAqB,CACrB,qCAA+C,CAG/C,eAAgB,CADhB,YAAa,CAPb,cAAe,CAEf,UAAW,CADX,QAAS,CAQT,YAEF,CAEA,oBACE,yBACF,CAEA,sBACE,yBACF,CAEA,mBACE,GAEE,SAAU,CADV,0BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAGA,SAME,iCAAkC,CAFlC,wBAA6B,CAC7B,iBAAkB,CADlB,wBAA6B,CAF7B,WAAY,CADZ,UAMF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAGA,gBAEE,WAAkB,CADlB,iBAEF,CAEA,sBAYE,iCAAkC,CAFlC,qBAAiC,CACjC,iBAAkB,CADlB,sBAAiC,CATjC,UAAW,CAKX,WAAY,CAFZ,QAAS,CAIT,gBAAiB,CADjB,eAAgB,CALhB,iBAAkB,CAClB,OAAQ,CAER,UAQF,CAGA,YACE,uCACF,CAEA,kBAEE,+BAA0C,CAD1C,0BAEF,CAGA,kBAEE,8BAA6C,CAD7C,YAEF,CAGA,mCACE,eACE,wBAAyB,CACzB,oBAAqB,CACrB,aACF,CACF,CAGA,aACE,UACE,YACF,CACF,CAGA,+BACE,kBACE,gBACF,CACF,CAGA,uCACE,sDAGE,cACF,CAEA,EAEE,kCAAqC,CADrC,mCAEF,CACF", "sources": ["index.css", "App.css"], "sourcesContent": ["@tailwind base;\r\n@tailwind components;\r\n@tailwind utilities;\r\n\r\nbody {\r\n    margin: 0;\r\n    font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Roboto\",\r\n        \"Oxygen\", \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\",\r\n        \"Helvetica Neue\", sans-serif;\r\n    -webkit-font-smoothing: antialiased;\r\n    -moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\ncode {\r\n    font-family: source-code-pro, Menlo, Monaco, Consolas, \"Courier New\",\r\n        monospace;\r\n}\r\n", "/* Tailwind CSS base styles are imported through index.css */\r\n\r\n.App {\r\n  text-align: center;\r\n}\r\n\r\n/* Custom animations for upload progress */\r\n.upload-progress {\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.upload-progress::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: -100%;\r\n  height: 100%;\r\n  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);\r\n  animation: loading 1.5s infinite;\r\n}\r\n\r\n@keyframes loading {\r\n  0% { left: -100%; }\r\n  100% { left: 100%; }\r\n}\r\n\r\n/* Drag and drop styles */\r\n.drag-active {\r\n  border-color: #3b82f6;\r\n  background-color: #eff6ff;\r\n}\r\n\r\n/* File upload animations */\r\n.file-enter {\r\n  opacity: 0;\r\n  transform: translateY(20px);\r\n}\r\n\r\n.file-enter-active {\r\n  opacity: 1;\r\n  transform: translateY(0);\r\n  transition: opacity 300ms, transform 300ms;\r\n}\r\n\r\n.file-exit {\r\n  opacity: 1;\r\n}\r\n\r\n.file-exit-active {\r\n  opacity: 0;\r\n  transform: translateY(-20px);\r\n  transition: opacity 300ms, transform 300ms;\r\n}\r\n\r\n/* Status indicators */\r\n.status-indicator {\r\n  position: relative;\r\n  display: inline-block;\r\n}\r\n\r\n.status-indicator.pulsing::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  border-radius: inherit;\r\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\r\n  background-color: inherit;\r\n}\r\n\r\n@keyframes pulse {\r\n  0%, 100% {\r\n    opacity: 1;\r\n  }\r\n  50% {\r\n    opacity: 0.5;\r\n  }\r\n}\r\n\r\n/* Service grid animations */\r\n.service-grid {\r\n  display: grid;\r\n  gap: 0.5rem;\r\n  grid-template-columns: repeat(5, 1fr);\r\n}\r\n\r\n.service-status {\r\n  padding: 0.5rem;\r\n  border-radius: 0.375rem;\r\n  text-align: center;\r\n  font-size: 0.75rem;\r\n  font-weight: 500;\r\n  transition: all 0.2s;\r\n}\r\n\r\n.service-status.success {\r\n  background-color: #dcfce7;\r\n  color: #166534;\r\n}\r\n\r\n.service-status.error {\r\n  background-color: #fecaca;\r\n  color: #991b1b;\r\n}\r\n\r\n.service-status.pending {\r\n  background-color: #fef3c7;\r\n  color: #92400e;\r\n}\r\n\r\n.service-status.uploading {\r\n  background-color: #dbeafe;\r\n  color: #1e40af;\r\n}\r\n\r\n/* Responsive design */\r\n@media (max-width: 768px) {\r\n  .service-grid {\r\n    grid-template-columns: repeat(3, 1fr);\r\n  }\r\n}\r\n\r\n@media (max-width: 640px) {\r\n  .service-grid {\r\n    grid-template-columns: repeat(2, 1fr);\r\n  }\r\n}\r\n\r\n/* Upload queue styles */\r\n.upload-queue {\r\n  max-height: 300px;\r\n  overflow-y: auto;\r\n}\r\n\r\n.upload-queue::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.upload-queue::-webkit-scrollbar-track {\r\n  background: #f1f5f9;\r\n  border-radius: 3px;\r\n}\r\n\r\n.upload-queue::-webkit-scrollbar-thumb {\r\n  background: #cbd5e1;\r\n  border-radius: 3px;\r\n}\r\n\r\n.upload-queue::-webkit-scrollbar-thumb:hover {\r\n  background: #94a3b8;\r\n}\r\n\r\n/* Progress bar styles */\r\n.progress-bar {\r\n  height: 4px;\r\n  background-color: #e5e7eb;\r\n  border-radius: 2px;\r\n  overflow: hidden;\r\n}\r\n\r\n.progress-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #3b82f6, #1d4ed8);\r\n  border-radius: 2px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n/* Bandwidth indicator */\r\n.bandwidth-indicator {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 0.5rem;\r\n  font-size: 0.875rem;\r\n}\r\n\r\n.bandwidth-bar {\r\n  width: 100px;\r\n  height: 8px;\r\n  background-color: #e5e7eb;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.bandwidth-fill {\r\n  height: 100%;\r\n  background: linear-gradient(90deg, #10b981, #059669);\r\n  border-radius: 4px;\r\n  transition: width 0.3s ease;\r\n}\r\n\r\n/* System status cards */\r\n.status-card {\r\n  background: white;\r\n  border-radius: 0.5rem;\r\n  padding: 1rem;\r\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #e5e7eb;\r\n}\r\n\r\n.status-card h3 {\r\n  font-size: 0.875rem;\r\n  font-weight: 600;\r\n  color: #374151;\r\n  margin-bottom: 0.5rem;\r\n}\r\n\r\n.status-value {\r\n  font-size: 1.5rem;\r\n  font-weight: 700;\r\n  color: #111827;\r\n}\r\n\r\n.status-label {\r\n  font-size: 0.75rem;\r\n  color: #6b7280;\r\n}\r\n\r\n/* Embed code preview */\r\n.embed-preview {\r\n  background-color: #f8fafc;\r\n  border: 1px solid #e2e8f0;\r\n  border-radius: 0.375rem;\r\n  padding: 0.75rem;\r\n  font-family: 'Monaco', 'Consolas', monospace;\r\n  font-size: 0.75rem;\r\n  color: #475569;\r\n  white-space: pre-wrap;\r\n  word-break: break-all;\r\n  max-height: 120px;\r\n  overflow-y: auto;\r\n}\r\n\r\n/* Notification styles */\r\n.notification {\r\n  position: fixed;\r\n  top: 1rem;\r\n  right: 1rem;\r\n  background: white;\r\n  border-radius: 0.5rem;\r\n  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);\r\n  border-left: 4px solid #10b981;\r\n  padding: 1rem;\r\n  max-width: 400px;\r\n  z-index: 1000;\r\n  animation: slideIn 0.3s ease-out;\r\n}\r\n\r\n.notification.error {\r\n  border-left-color: #ef4444;\r\n}\r\n\r\n.notification.warning {\r\n  border-left-color: #f59e0b;\r\n}\r\n\r\n@keyframes slideIn {\r\n  from {\r\n    transform: translateX(100%);\r\n    opacity: 0;\r\n  }\r\n  to {\r\n    transform: translateX(0);\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n/* Loading spinner */\r\n.spinner {\r\n  width: 20px;\r\n  height: 20px;\r\n  border: 2px solid #e5e7eb;\r\n  border-top: 2px solid #3b82f6;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n@keyframes spin {\r\n  0% { transform: rotate(0deg); }\r\n  100% { transform: rotate(360deg); }\r\n}\r\n\r\n/* Button loading state */\r\n.button-loading {\r\n  position: relative;\r\n  color: transparent;\r\n}\r\n\r\n.button-loading::after {\r\n  content: '';\r\n  position: absolute;\r\n  top: 50%;\r\n  left: 50%;\r\n  width: 16px;\r\n  height: 16px;\r\n  margin-top: -8px;\r\n  margin-left: -8px;\r\n  border: 2px solid #ffffff;\r\n  border-top: 2px solid transparent;\r\n  border-radius: 50%;\r\n  animation: spin 1s linear infinite;\r\n}\r\n\r\n/* Hover effects */\r\n.hover-lift {\r\n  transition: transform 0.2s, box-shadow 0.2s;\r\n}\r\n\r\n.hover-lift:hover {\r\n  transform: translateY(-2px);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n/* Focus styles for accessibility */\r\n.focus-ring:focus {\r\n  outline: none;\r\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);\r\n}\r\n\r\n/* Dark mode styles (optional) */\r\n@media (prefers-color-scheme: dark) {\r\n  .embed-preview {\r\n    background-color: #1f2937;\r\n    border-color: #374151;\r\n    color: #d1d5db;\r\n  }\r\n}\r\n\r\n/* Print styles */\r\n@media print {\r\n  .no-print {\r\n    display: none;\r\n  }\r\n}\r\n\r\n/* High contrast mode */\r\n@media (prefers-contrast: high) {\r\n  .status-indicator {\r\n    border: 2px solid;\r\n  }\r\n}\r\n\r\n/* Reduced motion */\r\n@media (prefers-reduced-motion: reduce) {\r\n  .upload-progress::after,\r\n  .spinner,\r\n  .button-loading::after {\r\n    animation: none;\r\n  }\r\n  \r\n  * {\r\n    transition-duration: 0.01ms !important;\r\n    animation-duration: 0.01ms !important;\r\n  }\r\n}"], "names": [], "sourceRoot": ""}