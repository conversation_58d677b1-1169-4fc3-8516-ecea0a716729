# 🚀 Video Upload Automation - Complete Setup & Run Guide

## 📋 Project Overview

This is a comprehensive video upload automation tool that allows you to upload videos to 5 hosting services simultaneously:
- **Lulustream**
- **StreamP2P** 
- **RPMShare**
- **Filemoon**
- **UpnShare**

### ✨ Key Features
- ✅ **Real-time Progress Tracking** - See upload progress, speed, and completion percentage
- ✅ **Service Status Monitoring** - Individual progress for each hosting service
- ✅ **Dark Mode Theme** - Professional dark theme similar to app.emergent.sh
- ✅ **Temporary Folder Access** - Click folder icons to open upload locations
- ✅ **Audio Notifications** - Custom success/error sounds
- ✅ **Enhanced File Support** - Full MKV and additional video format support
- ✅ **Bandwidth Management** - Optimized 80% bandwidth usage
- ✅ **Concurrent Uploads** - Up to 5 simultaneous uploads

## 🛠️ Prerequisites

### Required Software
1. **Python 3.8+** - [Download Python](https://www.python.org/downloads/)
2. **Node.js 16+** - [Download Node.js](https://nodejs.org/)
3. **Yarn Package Manager** - Install via: `npm install -g yarn`
4. **Git** - [Download Git](https://git-scm.com/downloads)

### System Requirements
- **OS**: Windows 10/11, macOS, or Linux
- **RAM**: Minimum 4GB, Recommended 8GB+
- **Storage**: 2GB free space
- **Network**: Stable internet connection

## 📦 Installation Steps

### 1. Clone the Repository
```bash
git clone <your-repository-url>
cd video-upload-automation/video-upload-automation
```

### 2. Backend Setup (Python/FastAPI)
```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Frontend Setup (React)
```bash
# Navigate to frontend directory (from project root)
cd frontend

# Install dependencies
yarn install
```

### 4. Environment Configuration

#### Backend Environment (.env)
Create/update `backend/.env` file:
```env
# MongoDB Configuration (currently using in-memory storage)
# MONGO_URL="mongodb://localhost:27017"
# DB_NAME="video_upload_automation"
CORS_ORIGINS="*"

# API Keys for Video Hosting Services
LULUSTREAM_API_KEY="your_lulustream_api_key_here"
STREAMP2P_API_KEY="your_streamp2p_api_key_here"
RPMSHARE_API_KEY="your_rpmshare_api_key_here"
FILEMOON_API_KEY="your_filemoon_api_key_here"
UPNSHARE_API_KEY="your_upnshare_api_key_here"

# Upload Configuration
MAX_CONCURRENT_UPLOADS=5
BANDWIDTH_LIMIT_PERCENT=80
CHUNK_SIZE_MB=100
```

#### Frontend Environment (.env)
Update `frontend/.env` file:
```env
REACT_APP_BACKEND_URL=http://localhost:8001
WDS_SOCKET_PORT=443
```

### 5. Audio Notifications Setup (Optional)
Place your custom notification sounds in `frontend/public/audio/`:
- `success.mp3` - Plays when upload completes successfully
- `error.mp3` - Plays when upload fails

**Audio Requirements:**
- Format: MP3, WAV, or OGG
- Size: Under 1MB recommended
- Duration: 1-3 seconds for best UX

## 🚀 Running the Application

### Method 1: Manual Start (Recommended for Development)

#### Terminal 1 - Backend Server
```bash
cd backend
# Activate virtual environment if not already active
venv\Scripts\activate  # Windows
# source venv/bin/activate  # macOS/Linux

# Start FastAPI server
python server.py
```
**Backend will run on:** `http://localhost:8001`

#### Terminal 2 - Frontend Development Server
```bash
cd frontend
# Start React development server
yarn start
```
**Frontend will run on:** `http://localhost:3000`

### Method 2: Production Build

#### Build Frontend for Production
```bash
cd frontend
yarn build
```

#### Serve Production Build
```bash
# Install serve globally
npm install -g serve

# Serve the built frontend
serve -s build -l 3000
```

## 🎯 How to Use the Application

### 1. **Upload Videos**
- **Drag & Drop**: Drag video files into the upload zone
- **Browse Files**: Click "Browse Files" to select videos
- **Supported Formats**: MP4, AVI, MKV, MOV, WMV, FLV, WebM, M4V, 3GP, OGV, TS, MTS, M2TS
- **File Size**: 100 bytes to 15GB

### 2. **Monitor Progress**
- **Real-time Progress**: See upload percentage, speed, and data transferred
- **Service Status**: Monitor individual hosting service progress
- **System Resources**: View CPU, RAM usage, and active uploads

### 3. **Access Temporary Files**
- **Folder Icon**: Click the folder icon next to uploads to open temp directory
- **File Management**: Access uploaded files during processing

### 4. **Manage Uploads**
- **Cancel**: Stop active uploads
- **Delete**: Remove completed uploads
- **Download CSV**: Get embed codes and links
- **View Logs**: Check detailed upload logs

### 5. **Dark Mode**
- **Toggle**: Click 🌙/☀️ button in header
- **Auto-save**: Theme preference is remembered

## 🔧 Configuration Options

### Backend Configuration
- **MAX_CONCURRENT_UPLOADS**: Number of simultaneous uploads (default: 5)
- **BANDWIDTH_LIMIT_PERCENT**: Bandwidth usage limit (default: 80%)
- **CHUNK_SIZE_MB**: Upload chunk size (default: 100MB)

### API Keys Setup
1. **Obtain API Keys**: Get API keys from each hosting service
2. **Update .env**: Add keys to `backend/.env` file
3. **Restart Backend**: Restart the backend server to apply changes
4. **Verify Status**: Check "Hosting Services" section for "Ready" status

## 🐛 Troubleshooting

### Common Issues

#### Backend Won't Start
```bash
# Check Python version
python --version

# Reinstall dependencies
pip install -r requirements.txt --force-reinstall

# Check port availability
netstat -an | findstr :8001
```

#### Frontend Won't Start
```bash
# Clear node modules and reinstall
rm -rf node_modules yarn.lock
yarn install

# Check Node.js version
node --version
yarn --version
```

#### API Keys Not Working
1. Verify API keys are correct in `backend/.env`
2. Restart backend server after updating keys
3. Check service status in the UI
4. Review backend logs for authentication errors

#### Upload Failures
1. Check file format and size limits
2. Verify internet connection
3. Check backend logs for detailed errors
4. Ensure sufficient disk space

### Performance Optimization

#### For Better Upload Performance
1. **Increase Bandwidth Limit**: Adjust `BANDWIDTH_LIMIT_PERCENT` in .env
2. **Optimize Chunk Size**: Modify `CHUNK_SIZE_MB` based on connection
3. **Reduce Concurrent Uploads**: Lower `MAX_CONCURRENT_UPLOADS` for stability

#### For System Resources
1. **Monitor CPU/RAM**: Use system status indicators
2. **Close Unnecessary Apps**: Free up system resources
3. **Check Disk Space**: Ensure adequate storage for temp files

## 📁 Project Structure
```
video-upload-automation/
├── backend/
│   ├── server.py              # FastAPI backend server
│   ├── requirements.txt       # Python dependencies
│   └── .env                   # Backend environment variables
├── frontend/
│   ├── src/
│   │   ├── App.js            # Main React application
│   │   ├── App.css           # Application styles
│   │   └── index.js          # React entry point
│   ├── public/
│   │   ├── audio/            # Custom notification sounds
│   │   └── index.html        # HTML template
│   ├── package.json          # Frontend dependencies
│   └── .env                  # Frontend environment variables
├── .gitignore                # Git ignore rules
└── SETUP_AND_RUN_GUIDE.md   # This guide
```

## 🔒 Security Notes

### Environment Files
- ✅ `.env` files are in `.gitignore` - **NEVER commit API keys**
- ✅ Use environment variables for sensitive data
- ✅ Regularly rotate API keys

### Best Practices
1. **Keep API Keys Secret**: Never share or commit API keys
2. **Use HTTPS**: In production, use HTTPS for frontend
3. **Regular Updates**: Keep dependencies updated
4. **Backup Data**: Regularly backup important uploads

## 🎉 Success Indicators

### Application is Working When:
- ✅ Backend starts without errors on port 8001
- ✅ Frontend loads successfully on port 3000
- ✅ System status shows green indicators
- ✅ Hosting services show "Ready" status (when API keys configured)
- ✅ File uploads show progress bars and complete successfully
- ✅ Dark mode toggle works smoothly
- ✅ Folder icons open temporary directories
- ✅ Audio notifications play on success/error

## 📞 Support

If you encounter issues:
1. **Check Logs**: Review browser console and backend terminal
2. **Verify Setup**: Ensure all prerequisites are installed
3. **Test API Keys**: Verify hosting service credentials
4. **Check Network**: Ensure stable internet connection
5. **Review Guide**: Re-read this setup guide carefully

---

**🎯 You're all set!** Your video upload automation tool is ready to upload videos to multiple hosting services simultaneously with real-time progress tracking and a beautiful dark theme interface.