<!DOCTYPE html>
<html>
<head>
    <title>Upload Test</title>
</head>
<body>
    <h1>Upload Test</h1>
    <input type="file" id="fileInput" accept="video/*">
    <button onclick="testUpload()">Test Upload</button>
    <div id="result"></div>

    <script>
        async function testUpload() {
            const fileInput = document.getElementById('fileInput');
            const resultDiv = document.getElementById('result');
            
            if (!fileInput.files[0]) {
                resultDiv.innerHTML = 'Please select a file first';
                return;
            }
            
            const file = fileInput.files[0];
            const formData = new FormData();
            formData.append('file', file);
            formData.append('title', file.name.split('.')[0]);
            
            try {
                resultDiv.innerHTML = 'Uploading...';
                
                const response = await fetch('http://localhost:8001/api/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                resultDiv.innerHTML = `
                    <h3>Response:</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                    <p>Status: ${response.status}</p>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>Error:</h3>
                    <p>${error.message}</p>
                    <p>Check browser console for more details</p>
                `;
                console.error('Upload error:', error);
            }
        }
    </script>
</body>
</html>
