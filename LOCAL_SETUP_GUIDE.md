# Video Upload Automation - Local Setup Guide

## 🚀 Quick Start Guide

This application allows you to upload large video files (up to 15GB) to 5 different video hosting services simultaneously. It's optimized for local use without requiring a database.

## 📋 Prerequisites

### Required Software
- **Python 3.8+** - [Download here](https://www.python.org/downloads/)
- **Node.js 16+** - [Download here](https://nodejs.org/en/download/)
- **Git** (optional) - [Download here](https://git-scm.com/downloads)

### System Requirements
- **RAM**: Minimum 8GB (16GB recommended for large files)
- **Storage**: At least 20GB free space for temporary files
- **Internet**: Stable broadband connection (upload speed affects performance)
- **OS**: Windows 10/11, macOS 10.14+, or Linux

## 🛠️ Installation Steps

### Step 1: Download the Application

**Option A: Clone from Git (if available)**
```bash
git clone [repository-url]
cd video-upload-automation
```

**Option B: Download ZIP**
- Download and extract the application files
- Open terminal/command prompt in the extracted folder

### Step 2: Install Backend Dependencies

```bash
# Navigate to backend folder
cd backend

# Install Python dependencies
pip install -r requirements.txt

# Alternative if pip doesn't work:
pip3 install -r requirements.txt
```

If you encounter permission issues on macOS/Linux, use:
```bash
pip install --user -r requirements.txt
```

### Step 3: Install Frontend Dependencies

```bash
# Navigate to frontend folder (from project root)
cd frontend

# Install Node.js dependencies
npm install

# Alternative using yarn:
yarn install
```

## 🔧 Configuration

### Step 1: Set Up API Keys

Create a `.env` file in the `backend` folder with your API keys:

```env
# API Keys for Video Hosting Services
LULUSTREAM_API_KEY=your_lulustream_api_key_here
STREAMP2P_API_KEY=your_streamp2p_api_key_here
RPMSHARE_API_KEY=your_rpmshare_api_key_here
FILEMOON_API_KEY=your_filemoon_api_key_here
UPNSHARE_API_KEY=your_upnshare_api_key_here

# Performance Settings (Optional - defaults are optimized)
MAX_CONCURRENT_UPLOADS=3
BANDWIDTH_LIMIT_PERCENT=70
CHUNK_SIZE_MB=50
```

### Step 2: Get Your API Keys

You'll need to register and get API keys from each service:

1. **Lulustream** - Visit: https://lulustream.com/api.html
2. **StreamP2P** - Visit: https://streamp2p.com/api-document/
3. **RPMShare** - Visit: https://rpmshare.com/api-document/
4. **Filemoon** - Visit: https://filemoon.sx/api
5. **UpnShare** - Visit: https://upnshare.com/api-document/

### Step 3: Frontend Configuration

Create a `.env` file in the `frontend` folder:

```env
REACT_APP_BACKEND_URL=http://localhost:8001
```

## 🚀 Running the Application

### Method 1: Using Individual Commands (Recommended)

**Terminal 1 - Backend:**
```bash
cd backend
python server.py
```

**Terminal 2 - Frontend:**
```bash
cd frontend
npm start
```

### Method 2: Using Scripts (if available)

```bash
# Start both backend and frontend
npm run start:dev

# Or using separate scripts
npm run start:backend    # Starts backend only
npm run start:frontend   # Starts frontend only
```

## 🌐 Accessing the Application

1. Backend API: http://localhost:8001
2. Frontend Web App: http://localhost:3000
3. API Documentation: http://localhost:8001/docs

The web interface will automatically open in your default browser.

## 💻 System Optimization

### Performance Settings

The application is pre-configured to prevent system freezing:

- **Concurrent Uploads**: Limited to 3 simultaneous uploads
- **Bandwidth Usage**: Capped at 70% to leave resources for other tasks
- **Memory Management**: Files processed in 50MB chunks
- **CPU Usage**: Optimized to prevent overwhelming your system

### Troubleshooting Performance Issues

If your system becomes slow:

1. **Lower Concurrent Uploads**:
   ```env
   MAX_CONCURRENT_UPLOADS=2
   ```

2. **Reduce Bandwidth Usage**:
   ```env
   BANDWIDTH_LIMIT_PERCENT=50
   ```

3. **Smaller Chunk Size**:
   ```env
   CHUNK_SIZE_MB=25
   ```

## 📱 How to Use

### 1. Upload Videos
- Drag and drop video files into the upload area
- Or click "Browse Files" to select files
- Supported formats: MP4, AVI, MKV, MOV, WMV, FLV, WebM
- File size: 100 bytes to 15GB

### 2. Monitor Progress
- View real-time system status (CPU, RAM, Active uploads)
- Track individual upload progress
- Monitor service status

### 3. Manage Uploads
- **Cancel**: Stop active uploads using the "Cancel" button
- **View Logs**: Click "View Logs" to see detailed upload progress
- **Clear All**: Remove all uploads and logs using "Clear All"

### 4. Download Results
- Once uploads complete, download CSV files with embed codes
- Embed codes are formatted in the specified order:
  1. Lulustream
  2. StreamP2P  
  3. RPMShare
  4. Filemoon
  5. UpnShare

## 🔒 Security & Privacy

- **No Database Required**: All data stored in memory only
- **Local Processing**: Files never leave your machine until uploaded
- **API Keys**: Stored locally in .env files (never shared)
- **Temporary Files**: Automatically cleaned up after processing

## 🛡️ Safety Features

### System Protection
- **Resource Monitoring**: Real-time CPU and memory usage tracking
- **Upload Limits**: Prevents overwhelming your system
- **Emergency Stop**: "Clear All" button to stop all operations
- **Auto Cleanup**: Temporary files automatically removed

### Error Handling
- **Graceful Failures**: App continues working if one service fails
- **Detailed Logging**: View exactly what's happening with each upload
- **Recovery**: Restart individual uploads without affecting others

## 📊 Features

### ✅ Core Features
- Upload to 5 video hosting services simultaneously
- Drag-and-drop file interface
- Real-time progress tracking
- System resource monitoring
- Concurrent upload management
- CSV export of embed codes
- Detailed logging system
- Upload cancellation
- Queue management

### ✅ Safety Features
- Memory-only storage (no database required)
- Bandwidth limiting to prevent network congestion
- System resource monitoring
- Emergency stop functionality
- Automatic file cleanup
- Error recovery

## 🚨 Troubleshooting

### Common Issues

**Backend won't start:**
```bash
# Check Python version
python --version

# Install dependencies again
pip install -r requirements.txt

# Try running with Python 3 explicitly
python3 server.py
```

**Frontend won't start:**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
npm start
```

**Port conflicts:**
```bash
# Kill processes on default ports
# Windows:
netstat -ano | findstr :3000
netstat -ano | findstr :8001

# macOS/Linux:
lsof -ti:3000 | xargs kill -9
lsof -ti:8001 | xargs kill -9
```

**Upload failures:**
1. Check your API keys in `.env` file
2. Verify internet connection
3. Check file format and size
4. View logs for detailed error messages

### Performance Issues

**System too slow:**
1. Close other applications
2. Reduce `MAX_CONCURRENT_UPLOADS` to 1-2
3. Lower `BANDWIDTH_LIMIT_PERCENT` to 30-50%
4. Upload smaller files first

**Upload too slow:**
1. Check your internet upload speed
2. Increase `CHUNK_SIZE_MB` if you have good internet
3. Ensure no other applications are using bandwidth

## 📞 Support

### Getting Help

1. **Check Logs**: Use the "Show Logs" feature to see detailed error messages
2. **System Status**: Monitor the system status indicators in the top bar
3. **Clear and Restart**: Use "Clear All" and restart the application
4. **Check Configuration**: Verify your .env files are correctly configured

### Log Locations

- **System Logs**: Available in the web interface
- **Upload Logs**: Specific to each upload, viewable per upload
- **Backend Console**: Check the terminal where you ran `python server.py`
- **Frontend Console**: Check browser developer tools (F12)

## 🔄 Updates and Maintenance

### Regular Maintenance
- Clear temporary files: Use "Clear All" periodically
- Update API keys: If services change their keys
- Monitor system resources: Keep an eye on CPU/memory usage

### Stopping the Application
1. Use Ctrl+C in both terminal windows
2. Or close the terminal windows
3. Use "Clear All" before stopping to clean up properly

---

## ⚠️ Important Notes

1. **Internet Connection**: Stable broadband required for large file uploads
2. **System Resources**: Monitor CPU and memory usage during uploads
3. **File Sizes**: Larger files require more system resources
4. **API Limits**: Each service may have upload limits or quotas
5. **Temporary Storage**: Ensure sufficient disk space for temporary files

---

🎉 **You're all set!** The application is now ready to upload your videos to multiple hosting services simultaneously while keeping your system responsive and secure.