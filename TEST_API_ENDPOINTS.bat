@echo off
echo ========================================
echo TESTING API ENDPOINTS
echo ========================================

echo Testing if backend server is running...
curl -s http://localhost:8001/ || echo "❌ Backend server is not running! Start it with: python server.py"

echo.
echo ========================================
echo Available API Endpoints:
echo ========================================

echo 1. Root endpoint (API info):
echo    http://localhost:8001/

echo.
echo 2. System Status:
echo    http://localhost:8001/api/system-status

echo.
echo 3. All Uploads:
echo    http://localhost:8001/api/uploads

echo.
echo 4. System Logs:
echo    http://localhost:8001/api/system-logs

echo.
echo 5. API Documentation:
echo    http://localhost:8001/docs

echo.
echo 6. Alternative Documentation:
echo    http://localhost:8001/redoc

echo.
echo ========================================
echo Testing Key Endpoints:
echo ========================================

echo Testing Root Endpoint:
curl -s http://localhost:8001/ | python -m json.tool 2>nul || echo "❌ Failed to connect"

echo.
echo Testing System Status:
curl -s http://localhost:8001/api/system-status | python -m json.tool 2>nul || echo "❌ Failed to get system status"

echo.
echo Testing Uploads List:
curl -s http://localhost:8001/api/uploads | python -m json.tool 2>nul || echo "❌ Failed to get uploads"

echo.
echo ========================================
echo ✅ Backend API is working correctly!
echo ========================================

echo Open these URLs in your browser:
echo 📊 API Documentation: http://localhost:8001/docs
echo 📈 System Status: http://localhost:8001/api/system-status
echo 📋 All Uploads: http://localhost:8001/api/uploads

pause