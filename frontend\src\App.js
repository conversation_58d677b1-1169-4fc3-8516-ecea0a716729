import React, { useState, useEffect, useCallback } from 'react';
import './App.css';

function App() {
  const [files, setFiles] = useState([]);
  const [uploads, setUploads] = useState([]);
  const [systemStatus, setSystemStatus] = useState(null);
  const [systemLogs, setSystemLogs] = useState([]);
  const [uploadLogs, setUploadLogs] = useState({});
  const [selectedUploadLogs, setSelectedUploadLogs] = useState(null);
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [showLogs, setShowLogs] = useState(false);
  const [logsType, setLogsType] = useState('system');
  const [uploadProgress, setUploadProgress] = useState({});
  const [darkMode, setDarkMode] = useState(true);

  const backendUrl = process.env.REACT_APP_BACKEND_URL || 'http://localhost:8001';

  // Audio notification functions
  const playNotificationSound = (type) => {
    try {
      const audio = new Audio(`/audio/${type}.mp3`);
      audio.volume = 0.5; // Set volume to 50%
      audio.play().catch(() => {
        // Fallback to system notification if custom sound fails
        if (type === 'success') {
          // System success sound fallback
          const systemAudio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU+ltryxnkpBSl+zPLaizsIGGS57OGYSQwKTKXh8bllHgg2jdXzzn0vBSF1xe/eizEIHWq+8+OWT' );
          systemAudio.play().catch(() => {});
        } else {
          // System error sound fallback
          const systemAudio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU+ltryxnkpBSl+zPLaizsIGGS57OGYSQwKTKXh8bllHgg2jdXzzn0vBSF1xe/eizEIHWq+8+OWT' );
          systemAudio.play().catch(() => {});
        }
      });
    } catch (error) {
      // Silent fallback
    }
  };

  // Utility functions
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatSpeed = (bytesPerSecond) => {
    if (!bytesPerSecond) return '0 B/s';
    return formatFileSize(bytesPerSecond) + '/s';
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'uploading': case 'processing': return 'text-blue-600';
      case 'failed': case 'error': return 'text-red-600';
      case 'cancelled': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const getLogLevelColor = (level) => {
    switch (level) {
      case 'ERROR': return 'text-red-600';
      case 'WARNING': return 'text-yellow-600';
      case 'INFO': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const removeFile = (fileToRemove) => {
    setFiles(prevFiles => prevFiles.filter(file => file !== fileToRemove));
  };

  const openTempFolder = async (uploadId) => {
    try {
      const response = await fetch(`${backendUrl}/api/temp-folder/${uploadId}`);
      const data = await response.json();
      if (data.temp_path) {
        // This will work on Windows - opens the folder in Explorer
        window.open(`file:///${data.temp_path.replace(/\\/g, '/')}`);
      }
    } catch (error) {
      // Silently handle error
    }
  };

  // Fetch system status
  const fetchSystemStatus = useCallback(async () => {
    try {
      const response = await fetch(`${backendUrl}/api/system-status`);
      const data = await response.json();
      setSystemStatus(data);
    } catch (error) {
      // Silently handle error to avoid console pollution in production
      // Error will be reflected in UI through missing status data
    }
  }, [backendUrl]);

  // Fetch uploads
  const fetchUploads = useCallback(async () => {
    try {
      const response = await fetch(`${backendUrl}/api/uploads`);
      const data = await response.json();
      setUploads(data.uploads || []);
    } catch (error) {
      // Silently handle error to avoid console pollution in production
      // Error will be reflected in UI through missing uploads data
    }
  }, [backendUrl]);

  // Fetch system logs
  const fetchSystemLogs = useCallback(async () => {
    try {
      const response = await fetch(`${backendUrl}/api/system-logs`);
      const data = await response.json();
      setSystemLogs(data.logs || []);
    } catch (error) {
      // Silently handle error to avoid console pollution in production
      // Error will be reflected in UI through missing logs data
    }
  }, [backendUrl]);

  // Fetch upload logs
  const fetchUploadLogs = useCallback(async (uploadId) => {
    try {
      const response = await fetch(`${backendUrl}/api/logs/${uploadId}`);
      const data = await response.json();
      setUploadLogs(prev => ({
        ...prev,
        [uploadId]: data.logs || []
      }));
    } catch (error) {
      // Silently handle error to avoid console pollution in production
      // Error will be reflected in UI through missing upload logs data
    }
  }, [backendUrl]);

  // Poll for updates
  useEffect(() => {
    fetchSystemStatus();
    fetchUploads();
    fetchSystemLogs();
    
    const interval = setInterval(() => {
      fetchSystemStatus();
      fetchUploads();
      if (showLogs && logsType === 'system') {
        fetchSystemLogs();
      }
    }, 3000);

    return () => clearInterval(interval);
  }, [fetchSystemStatus, fetchUploads, fetchSystemLogs, showLogs, logsType]);

  // Handle file selection
  const handleFiles = (selectedFiles) => {
    const videoFiles = Array.from(selectedFiles).filter(file => {
      // Enhanced video file detection including MKV and other formats
      const videoExtensions = /\.(mp4|avi|mkv|mov|wmv|flv|webm|m4v|3gp|ogv|ts|mts|m2ts)$/i;
      const isVideo = file.type.startsWith('video/') || 
                     videoExtensions.test(file.name) ||
                     file.type === 'video/x-matroska' || // MKV MIME type
                     file.type === 'video/x-msvideo' ||  // AVI MIME type
                     file.type === '';  // Some systems don't set MIME type for MKV
      const sizeOK = file.size >= 100 && file.size <= 15 * 1024 * 1024 * 1024; // 100 bytes to 15GB
      return isVideo && sizeOK;
    });

    if (videoFiles.length === 0) {
      alert('Please select valid video files (100 bytes - 15GB)');
      return;
    }

    setFiles(prevFiles => [...prevFiles, ...videoFiles]);
  };

  // Handle drag and drop
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  // Upload file with progress tracking
  const uploadFile = async (file) => {
    setUploading(true);
    const fileId = `${file.name}_${Date.now()}`;
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('title', file.name.split('.')[0]);

      // Initialize progress tracking
      setUploadProgress(prev => ({
        ...prev,
        [fileId]: { 
          progress: 0, 
          speed: 0, 
          uploaded: 0, 
          total: file.size,
          startTime: Date.now()
        }
      }));

      const xhr = new XMLHttpRequest();
      
      return new Promise((resolve, reject) => {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            const elapsed = (Date.now() - uploadProgress[fileId]?.startTime) / 1000;
            const speed = elapsed > 0 ? event.loaded / elapsed : 0;
            
            setUploadProgress(prev => ({
              ...prev,
              [fileId]: {
                ...prev[fileId],
                progress,
                speed,
                uploaded: event.loaded,
                total: event.total
              }
            }));
          }
        });

        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            const result = JSON.parse(xhr.responseText);
            if (result.success) {
              setFiles(prevFiles => prevFiles.filter(f => f !== file));
              fetchUploads();
              playNotificationSound('success'); // Play success sound
              resolve(result);
            } else {
              playNotificationSound('error'); // Play error sound
              reject(new Error(result.message || 'Upload failed'));
            }
          } else {
            playNotificationSound('error'); // Play error sound
            reject(new Error('Upload failed'));
          }
          
          // Clean up progress tracking
          setUploadProgress(prev => {
            const newProgress = { ...prev };
            delete newProgress[fileId];
            return newProgress;
          });
        });

        xhr.addEventListener('error', () => {
          playNotificationSound('error'); // Play error sound
          reject(new Error('Upload failed'));
          setUploadProgress(prev => {
            const newProgress = { ...prev };
            delete newProgress[fileId];
            return newProgress;
          });
        });

        xhr.open('POST', `${backendUrl}/api/upload`);
        xhr.send(formData);
      });
    } catch (error) {
      alert(`Upload failed: ${error.message}`);
      setUploadProgress(prev => {
        const newProgress = { ...prev };
        delete newProgress[fileId];
        return newProgress;
      });
    } finally {
      setUploading(false);
    }
  };

  // Upload all files
  const uploadAllFiles = async () => {
    if (files.length === 0) {
      alert('No files selected');
      return;
    }

    // Upload files one by one to manage system resources
    for (const file of files) {
      await uploadFile(file);
      // Small delay between uploads to prevent overwhelming system
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  };

  // Cancel upload
  const cancelUpload = async (uploadId) => {
    if (!window.confirm('Are you sure you want to cancel this upload?')) {
      return;
    }

    try {
      const response = await fetch(`${backendUrl}/api/cancel/${uploadId}`, {
        method: 'POST',
      });

      if (response.ok) {
        fetchUploads();
      } else {
        throw new Error('Failed to cancel upload');
      }
    } catch (error) {
      console.error('Cancel failed:', error);
      alert('Failed to cancel upload');
    }
  };

  // Clear all data
  const clearAllData = async () => {
    if (!window.confirm('Are you sure you want to clear all uploads, logs, and queues? This cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`${backendUrl}/api/clear-all`, {
        method: 'POST',
      });

      if (response.ok) {
        setFiles([]);
        setUploads([]);
        setSystemLogs([]);
        setUploadLogs({});
        setSelectedUploadLogs(null);
        fetchSystemStatus();
      } else {
        throw new Error('Failed to clear data');
      }
    } catch (error) {
      console.error('Clear failed:', error);
      alert('Failed to clear data');
    }
  };

  // Remove file from queue
  const removeFile = (fileToRemove) => {
    setFiles(files.filter(file => file !== fileToRemove));
  };

  // Download CSV
  const downloadCSV = async (uploadId) => {
    try {
      const response = await fetch(`${backendUrl}/api/download-csv/${uploadId}`);
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `embed_codes_${uploadId}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
      } else {
        throw new Error('Failed to download CSV');
      }
    } catch (error) {
      console.error('Download failed:', error);
      alert('Failed to download CSV file');
    }
  };

  // Delete upload
  const deleteUpload = async (uploadId) => {
    if (!window.confirm('Are you sure you want to delete this upload?')) {
      return;
    }

    try {
      const response = await fetch(`${backendUrl}/api/uploads/${uploadId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchUploads();
        // Remove from logs if selected
        if (selectedUploadLogs?.upload_id === uploadId) {
          setSelectedUploadLogs(null);
        }
      } else {
        throw new Error('Failed to delete upload');
      }
    } catch (error) {
      console.error('Delete failed:', error);
      alert('Failed to delete upload');
    }
  };

  // Show upload logs
  const showUploadLogs = (uploadId) => {
    fetchUploadLogs(uploadId).then(() => {
      setSelectedUploadLogs({
        upload_id: uploadId,
        logs: uploadLogs[uploadId] || []
      });
      setShowLogs(true);
      setLogsType('upload');
    });
  };

  // Additional utility functions
  const downloadCSV = async (uploadId) => {
    try {
      const response = await fetch(`${backendUrl}/api/download-csv/${uploadId}`);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `upload_${uploadId}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      alert('Failed to download CSV');
    }
  };

  const deleteUpload = async (uploadId) => {
    if (!window.confirm('Are you sure you want to delete this upload?')) {
      return;
    }
    try {
      const response = await fetch(`${backendUrl}/api/delete/${uploadId}`, {
        method: 'DELETE',
      });
      if (response.ok) {
        fetchUploads();
      }
    } catch (error) {
      alert('Failed to delete upload');
    }
  };

  const showUploadLogs = (uploadId) => {
    setSelectedUploadLogs({ upload_id: uploadId, logs: uploadLogs[uploadId] || [] });
    setLogsType('upload');
    setShowLogs(true);
    fetchUploadLogs(uploadId);
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'uploading': case 'processing': return 'text-blue-600';
      case 'failed': return 'text-red-600';
      case 'cancelled': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  // Get log level color
  const getLogLevelColor = (level) => {
    switch (level) {
      case 'SUCCESS': return 'text-green-600';
      case 'ERROR': return 'text-red-600';
      case 'WARNING': return 'text-yellow-600';
      case 'INFO': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className={`min-h-screen transition-colors duration-200 ${
      darkMode ? 'bg-gray-900' : 'bg-gray-50'
    }`}>
      {/* Header */}
      <div className={`shadow-sm border-b transition-colors duration-200 ${
        darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className={`text-2xl font-bold transition-colors duration-200 ${
                darkMode ? 'text-white' : 'text-gray-900'
              }`}>Video Upload Automation</h1>
              <p className={`transition-colors duration-200 ${
                darkMode ? 'text-gray-300' : 'text-gray-600'
              }`}>Upload videos to 5 hosting services simultaneously</p>
            </div>
            
            <div className="flex items-center space-x-4">
              {systemStatus && (
                <div className={`flex items-center space-x-6 text-sm transition-colors duration-200 ${
                  darkMode ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${systemStatus.cpu_usage < 80 ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span>CPU: {systemStatus.cpu_usage.toFixed(1)}%</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${systemStatus.memory_usage < 80 ? 'bg-green-500' : 'bg-red-500'}`}></div>
                    <span>RAM: {systemStatus.memory_usage.toFixed(1)}%</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${systemStatus.active_uploads < systemStatus.max_concurrent ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                    <span>Active: {systemStatus.active_uploads}/{systemStatus.max_concurrent}</span>
                  </div>
                  <div>
                    Services: {systemStatus.services_configured}/5
                  </div>
                </div>
              )}
              
              <div className="flex space-x-2">
                <button
                  onClick={() => setDarkMode(!darkMode)}
                  className={`p-2 rounded-md text-sm transition-colors duration-200 ${
                    darkMode 
                      ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                  title="Toggle dark mode"
                >
                  {darkMode ? '☀️' : '🌙'}
                </button>
                <button
                  onClick={() => { setShowLogs(true); setLogsType('system'); }}
                  className={`px-3 py-1 rounded text-sm transition-colors duration-200 ${
                    darkMode
                      ? 'bg-blue-700 text-white hover:bg-blue-600'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  }`}
                >
                  Show Logs
                </button>
                <button
                  onClick={clearAllData}
                  className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors duration-200"
                >
                  Clear All
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          
          {/* Upload Section */}
          <div className="space-y-6">
            <div className={`rounded-lg shadow-sm border p-6 transition-colors duration-200 ${
              darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}>
              <h2 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
                darkMode ? 'text-white' : 'text-gray-900'
              }`}>Upload Videos</h2>
              
              {/* Drop Zone */}
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragActive 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                  <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                <p className="text-gray-600 mb-2">Drag and drop video files here, or</p>
                <label className="inline-block bg-blue-600 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-blue-700 transition-colors">
                  Browse Files
                  <input
                    type="file"
                    multiple
                    accept="video/*"
                    className="hidden"
                    onChange={(e) => handleFiles(e.target.files)}
                  />
                </label>
                <p className={`text-xs mt-2 transition-colors duration-200 ${
                  darkMode ? 'text-gray-400' : 'text-gray-500'
                }`}>
                  Supported formats: MP4, AVI, MKV, MOV, WMV, FLV, WebM, M4V, 3GP, OGV, TS, MTS, M2TS<br/>
                  File size: 100 bytes - 15GB
                </p>
              </div>
              
              {/* File Queue */}
              {files.length > 0 && (
                <div className="mt-6">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-medium text-gray-900">Upload Queue ({files.length})</h3>
                    <button
                      onClick={uploadAllFiles}
                      disabled={uploading}
                      className="bg-green-600 text-white px-4 py-2 rounded-md text-sm hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {uploading ? 'Uploading...' : 'Upload All'}
                    </button>
                  </div>
                  
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {files.map((file, index) => {
                      const fileId = `${file.name}_${Date.now()}`;
                      const progress = uploadProgress[fileId];
                      
                      return (
                        <div key={index} className={`p-3 rounded-md transition-colors duration-200 ${
                          darkMode ? 'bg-gray-700' : 'bg-gray-50'
                        }`}>
                          <div className="flex items-center justify-between">
                            <div className="flex-1 min-w-0">
                              <p className={`text-sm font-medium truncate transition-colors duration-200 ${
                                darkMode ? 'text-white' : 'text-gray-900'
                              }`}>{file.name}</p>
                              <p className={`text-xs transition-colors duration-200 ${
                                darkMode ? 'text-gray-400' : 'text-gray-500'
                              }`}>{formatFileSize(file.size)}</p>
                              
                              {/* Progress bar */}
                              {progress && (
                                <div className="mt-2">
                                  <div className={`w-full bg-gray-200 rounded-full h-2 ${
                                    darkMode ? 'bg-gray-600' : 'bg-gray-200'
                                  }`}>
                                    <div 
                                      className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                                      style={{ width: `${progress.progress}%` }}
                                    ></div>
                                  </div>
                                  <div className={`text-xs mt-1 transition-colors duration-200 ${
                                    darkMode ? 'text-gray-400' : 'text-gray-500'
                                  }`}>
                                    {progress.progress}% • {formatSpeed(progress.speed)} • {formatFileSize(progress.uploaded)}/{formatFileSize(progress.total)}
                                  </div>
                                </div>
                              )}
                            </div>
                            <div className="flex items-center space-x-2 ml-4">
                              <button
                                onClick={() => uploadFile(file)}
                                disabled={uploading}
                                className={`text-sm transition-colors duration-200 ${
                                  darkMode 
                                    ? 'text-blue-400 hover:text-blue-300 disabled:opacity-50' 
                                    : 'text-blue-600 hover:text-blue-800 disabled:opacity-50'
                                }`}
                              >
                                Upload
                              </button>
                              <button
                                onClick={() => removeFile(file)}
                                className={`text-sm transition-colors duration-200 ${
                                  darkMode 
                                    ? 'text-red-400 hover:text-red-300' 
                                    : 'text-red-600 hover:text-red-800'
                                }`}
                              >
                                Remove
                              </button>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
            
            {/* Service Status */}
            <div className={`rounded-lg shadow-sm border p-6 transition-colors duration-200 ${
              darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}>
              <h2 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
                darkMode ? 'text-white' : 'text-gray-900'
              }`}>Hosting Services</h2>
              <div className="grid grid-cols-1 gap-3">
                {[
                  { name: 'Lulustream', key: 'lulustream' },
                  { name: 'StreamP2P', key: 'streamp2p' },
                  { name: 'RPMShare', key: 'rpmshare' },
                  { name: 'Filemoon', key: 'filemoon' },
                  { name: 'UpnShare', key: 'upnshare' }
                ].map(service => {
                  const serviceData = systemStatus?.services?.[service.key];
                  const isConfigured = serviceData?.configured;
                  const activeUploads = serviceData?.active_uploads || 0;
                  const progress = serviceData?.progress;
                  
                  return (
                    <div key={service.key} className={`p-3 rounded-md transition-colors duration-200 ${
                      darkMode ? 'bg-gray-700' : 'bg-gray-50'
                    }`}>
                      <div className="flex items-center justify-between mb-2">
                        <span className={`font-medium transition-colors duration-200 ${
                          darkMode ? 'text-white' : 'text-gray-900'
                        }`}>{service.name}</span>
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full ${
                            isConfigured 
                              ? activeUploads > 0 ? 'bg-blue-500' : 'bg-green-500'
                              : 'bg-gray-400'
                          }`}></div>
                          <span className={`text-sm transition-colors duration-200 ${
                            darkMode ? 'text-gray-300' : 'text-gray-600'
                          }`}>
                            {isConfigured 
                              ? activeUploads > 0 ? `Uploading (${activeUploads})` : 'Ready'
                              : 'Not Configured'
                            }
                          </span>
                        </div>
                      </div>
                      
                      {/* Progress bar for active uploads */}
                      {isConfigured && activeUploads > 0 && progress && (
                        <div className="mt-2">
                          <div className={`w-full bg-gray-200 rounded-full h-2 ${
                            darkMode ? 'bg-gray-600' : 'bg-gray-200'
                          }`}>
                            <div 
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                              style={{ width: `${progress.percentage || 0}%` }}
                            ></div>
                          </div>
                          <div className={`text-xs mt-1 transition-colors duration-200 ${
                            darkMode ? 'text-gray-400' : 'text-gray-500'
                          }`}>
                            {progress.percentage ? `${progress.percentage}%` : 'Processing...'}
                            {progress.speed && ` • ${formatSpeed(progress.speed)}`}
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
              <p className={`text-xs mt-3 transition-colors duration-200 ${
                darkMode ? 'text-gray-400' : 'text-gray-500'
              }`}>
                {systemStatus?.services_configured > 0 
                  ? `${systemStatus.services_configured}/5 services configured and ready`
                  : 'Configure API keys in the .env file to activate services'
                }
              </p>
            </div>
          </div>
          
          {/* Upload History */}
          <div className={`rounded-lg shadow-sm border p-6 transition-colors duration-200 ${
            darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
          }`}>
            <h2 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
              darkMode ? 'text-white' : 'text-gray-900'
            }`}>Upload History</h2>
            
            {uploads.length === 0 ? (
              <p className={`text-center py-8 transition-colors duration-200 ${
                darkMode ? 'text-gray-400' : 'text-gray-500'
              }`}>No uploads yet</p>
            ) : (
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {uploads.map(upload => (
                  <div key={upload.upload_id} className={`border rounded-lg p-4 transition-colors duration-200 ${
                    darkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-200 bg-white'
                  }`}>
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2 flex-1 min-w-0">
                        <h3 className={`font-medium truncate transition-colors duration-200 ${
                          darkMode ? 'text-white' : 'text-gray-900'
                        }`}>{upload.filename}</h3>
                        {/* Folder access icon */}
                        <button
                          onClick={() => openTempFolder(upload.upload_id)}
                          className={`p-1 rounded hover:bg-gray-200 transition-colors duration-200 ${
                            darkMode ? 'hover:bg-gray-600 text-gray-400' : 'hover:bg-gray-200 text-gray-500'
                          }`}
                          title="Open temporary folder"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z" />
                          </svg>
                        </button>
                      </div>
                      <span className={`text-sm font-medium ${getStatusColor(upload.status)}`}>
                        {upload.status.charAt(0).toUpperCase() + upload.status.slice(1)}
                      </span>
                    </div>
                    
                    <div className={`text-sm mb-3 transition-colors duration-200 ${
                      darkMode ? 'text-gray-300' : 'text-gray-600'
                    }`}>
                      <p>Size: {formatFileSize(upload.file_size)}</p>
                      <p>Created: {new Date(upload.created_at).toLocaleString()}</p>
                    </div>
                    
                    {/* Service Results */}
                    {upload.final_results && (
                      <div className="grid grid-cols-5 gap-2 mb-3">
                        {['lulustream', 'streamp2p', 'rpmshare', 'filemoon', 'upnshare'].map(service => (
                          <div 
                            key={service}
                            className={`text-center p-2 rounded text-xs ${
                              upload.final_results[service] && upload.final_results[service].embed_code
                                ? darkMode ? 'bg-green-900 text-green-300' : 'bg-green-100 text-green-700'
                                : upload.final_results[service] && upload.final_results[service].error
                                ? darkMode ? 'bg-red-900 text-red-300' : 'bg-red-100 text-red-700'
                                : darkMode ? 'bg-gray-600 text-gray-300' : 'bg-gray-100 text-gray-700'
                            }`}
                          >
                            {service.charAt(0).toUpperCase() + service.slice(1)}
                          </div>
                        ))}
                      </div>
                    )}
                    
                    {/* Actions */}
                    <div className="flex items-center justify-between">
                      <div className="flex space-x-2">
                        {upload.csv_available && (
                          <button
                            onClick={() => downloadCSV(upload.upload_id)}
                            className={`px-3 py-1 rounded text-xs transition-colors duration-200 ${
                              darkMode 
                                ? 'bg-blue-700 text-white hover:bg-blue-600' 
                                : 'bg-blue-600 text-white hover:bg-blue-700'
                            }`}
                          >
                            Download CSV
                          </button>
                        )}
                        <button
                          onClick={() => showUploadLogs(upload.upload_id)}
                          className={`px-3 py-1 rounded text-xs transition-colors duration-200 ${
                            darkMode 
                              ? 'bg-gray-600 text-white hover:bg-gray-500' 
                              : 'bg-gray-600 text-white hover:bg-gray-700'
                          }`}
                        >
                          View Logs
                        </button>
                        {(upload.status === 'uploading' || upload.status === 'processing') && (
                          <button
                            onClick={() => cancelUpload(upload.upload_id)}
                            className="bg-yellow-600 text-white px-3 py-1 rounded text-xs hover:bg-yellow-700 transition-colors duration-200"
                          >
                            Cancel
                          </button>
                        )}
                      </div>
                      <button
                        onClick={() => deleteUpload(upload.upload_id)}
                        className={`text-xs transition-colors duration-200 ${
                          darkMode ? 'text-red-400 hover:text-red-300' : 'text-red-600 hover:text-red-800'
                        }`}
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Logs Modal */}
      {showLogs && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className={`rounded-lg max-w-4xl w-full max-h-[80vh] flex flex-col transition-colors duration-200 ${
            darkMode ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className={`flex items-center justify-between p-4 border-b transition-colors duration-200 ${
              darkMode ? 'border-gray-700' : 'border-gray-200'
            }`}>
              <div className="flex space-x-4">
                <h3 className={`text-lg font-semibold transition-colors duration-200 ${
                  darkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  {logsType === 'system' ? 'System Logs' : `Upload Logs: ${selectedUploadLogs?.upload_id}`}
                </h3>
                <div className="flex space-x-2">
                  <button
                    onClick={() => { setLogsType('system'); fetchSystemLogs(); }}
                    className={`px-3 py-1 rounded text-sm transition-colors duration-200 ${
                      logsType === 'system' 
                        ? 'bg-blue-600 text-white' 
                        : darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'
                    }`}
                  >
                    System
                  </button>
                  {selectedUploadLogs && (
                    <button
                      onClick={() => setLogsType('upload')}
                      className={`px-3 py-1 rounded text-sm transition-colors duration-200 ${
                        logsType === 'upload' 
                          ? 'bg-blue-600 text-white' 
                          : darkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-200 text-gray-700'
                      }`}
                    >
                      Upload
                    </button>
                  )}
                </div>
              </div>
              <button
                onClick={() => setShowLogs(false)}
                className={`transition-colors duration-200 ${
                  darkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="flex-1 overflow-y-auto p-4">
              <div className="space-y-2">
                {(logsType === 'system' ? systemLogs : selectedUploadLogs?.logs || []).map((log, index) => (
                  <div key={index} className="flex items-start space-x-3 text-sm font-mono">
                    <span className={`flex-shrink-0 transition-colors duration-200 ${
                      darkMode ? 'text-gray-400' : 'text-gray-500'
                    }`}>
                      {new Date(log.timestamp).toLocaleTimeString()}
                    </span>
                    <span className={`flex-shrink-0 ${getLogLevelColor(log.level)}`}>
                      [{log.level}]
                    </span>
                    <span className={`flex-1 transition-colors duration-200 ${
                      darkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>{log.message}</span>
                  </div>
                ))}
                
                {(logsType === 'system' ? systemLogs : selectedUploadLogs?.logs || []).length === 0 && (
                  <p className={`text-center py-8 transition-colors duration-200 ${
                    darkMode ? 'text-gray-400' : 'text-gray-500'
                  }`}>No logs available</p>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Footer */}
      <div className={`border-t mt-12 transition-colors duration-200 ${
        darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <p className={`text-center text-sm transition-colors duration-200 ${
            darkMode ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Video Upload Automation - Upload to multiple hosting services simultaneously
          </p>
        </div>
      </div>
    </div>
  );
}

export default App;