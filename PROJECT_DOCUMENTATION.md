# Video Upload Automation - Complete Project Documentation

## 📋 Project Overview

**Video Upload Automation** is a comprehensive web application designed to upload large video files (up to 15GB) to 5 different video hosting services simultaneously. The application features a modern React frontend with drag-and-drop functionality and a robust FastAPI backend with concurrent upload management.

### 🎯 Key Features
- **Multi-Service Upload**: Simultaneously upload to 5 video hosting platforms
- **Large File Support**: Handle files up to 15GB with optimized memory management
- **Concurrent Processing**: Configurable concurrent uploads with bandwidth limiting
- **Real-time Monitoring**: System resource tracking and upload progress
- **CSV Export**: Generate embed codes in specified format
- **Queue Management**: Upload queue with individual file control
- **In-Memory Storage**: No database required - all data stored in memory
- **Responsive UI**: Modern React interface with Tailwind CSS

## 🏗️ Architecture

### Backend (FastAPI)
- **Framework**: FastAPI with async/await support
- **Language**: Python 3.8+
- **Storage**: In-memory storage (no database required)
- **Concurrency**: AsyncIO with semaphore-based upload limiting
- **File Handling**: Chunked processing to prevent memory issues

### Frontend (React)
- **Framework**: React 19.0.0 with functional components
- **Styling**: Tailwind CSS for responsive design
- **Build Tool**: Create React App with CRACO configuration
- **State Management**: React hooks (useState, useEffect)
- **HTTP Client**: Native fetch API

### Supported Video Hosting Services
1. **Lulustream** - `luluvid.com/e/{filecode}`
2. **StreamP2P** - `streamdb.p2pstream.online/#{video_id}`
3. **RPMShare** - `streamdb.rpmstream.online/#{video_id}`
4. **Filemoon** - `filemoon.to/e/{file_id}`
5. **UpnShare** - `streamdb.upns.online/#{video_id}`

## 📁 Project Structure

```
video-upload-automation/
├── backend/
│   ├── server.py              # Main FastAPI application
│   └── requirements.txt       # Python dependencies
├── frontend/
│   ├── src/
│   │   ├── App.js            # Main React component
│   │   ├── App.css           # Component styles
│   │   ├── index.js          # React entry point
│   │   └── index.css         # Global styles
│   ├── public/
│   │   ├── index.html        # HTML template
│   │   └── audio/            # Audio assets
│   ├── package.json          # Node.js dependencies
│   ├── tailwind.config.js    # Tailwind configuration
│   ├── craco.config.js       # Build configuration
│   └── postcss.config.js     # PostCSS configuration
├── tests/
│   └── __init__.py           # Test package
├── backend_test.py           # Comprehensive API testing
├── test_result.md           # Testing protocol and results
├── LOCAL_SETUP_GUIDE.md     # Detailed setup instructions
├── README.md                # Basic project info
├── yarn.lock                # Yarn lock file
└── .gitignore               # Git ignore rules
```

## 🔧 Technical Implementation

### Backend Core Components

#### 1. Video Hosting Service Classes
Each service implements the `VideoHostService` base class:

```python
class VideoHostService:
    def __init__(self, api_key: str)
    async def get_session()
    async def close_session()
    async def upload_file(file_path, filename, upload_id, progress_callback)
```

#### 2. Upload Management System
- **Semaphore Control**: Limits concurrent uploads (default: 3)
- **Background Tasks**: Async processing with FastAPI BackgroundTasks
- **Progress Tracking**: Real-time status updates stored in memory
- **Error Handling**: Graceful failure handling per service

#### 3. In-Memory Storage
```python
uploads_storage = {}    # Upload records and results
upload_logs = {}       # Per-upload logging
active_uploads = {}    # Currently processing uploads
upload_queue = []      # Upload queue management
system_logs = []       # System-wide logging
```

#### 4. API Endpoints
- `POST /api/upload` - File upload with validation
- `GET /api/status/{upload_id}` - Upload progress tracking
- `GET /api/uploads` - List all uploads
- `GET /api/logs/{upload_id}` - Upload-specific logs
- `GET /api/system-logs` - System logs
- `GET /api/system-status` - Resource monitoring
- `GET /api/download-csv/{upload_id}` - CSV download
- `POST /api/cancel/{upload_id}` - Cancel upload
- `POST /api/clear-all` - Clear all data
- `DELETE /api/uploads/{upload_id}` - Delete upload

### Frontend Core Components

#### 1. Main App Component (`App.js`)
- **File Management**: Drag-and-drop with validation
- **Upload Queue**: Individual and batch upload controls
- **Real-time Updates**: 3-second polling for status updates
- **System Monitoring**: CPU, RAM, and upload status display
- **Log Viewer**: Modal for system and upload logs

#### 2. Key Features Implementation
- **Drag & Drop**: HTML5 drag events with visual feedback
- **File Validation**: Size (100 bytes - 15GB) and type checking
- **Progress Tracking**: Real-time upload status and progress
- **CSV Download**: Direct file download from backend
- **Responsive Design**: Mobile-optimized with Tailwind CSS

## ⚙️ Configuration

### Environment Variables

#### Backend (.env)
```env
# API Keys (Required for full functionality)
LULUSTREAM_API_KEY=your_key_here
STREAMP2P_API_KEY=your_key_here
RPMSHARE_API_KEY=your_key_here
FILEMOON_API_KEY=your_key_here
UPNSHARE_API_KEY=your_key_here

# Performance Settings (Optional)
MAX_CONCURRENT_UPLOADS=3
BANDWIDTH_LIMIT_PERCENT=70
CHUNK_SIZE_MB=50
CORS_ORIGINS=*
```

#### Frontend (.env)
```env
REACT_APP_BACKEND_URL=http://localhost:8001
```

### Performance Optimization
- **Concurrent Uploads**: Limited to 3 to prevent system overload
- **Bandwidth Limiting**: 70% usage cap to maintain system responsiveness
- **Memory Management**: 50MB chunks for large file processing
- **Resource Monitoring**: Real-time CPU and memory tracking

## 🚀 Installation & Setup

### Prerequisites
- Python 3.8+
- Node.js 16+
- 8GB RAM minimum (16GB recommended)
- 20GB free storage
- Stable internet connection

### Quick Start
1. **Backend Setup**:
   ```bash
   cd backend
   pip install -r requirements.txt
   python server.py
   ```

2. **Frontend Setup**:
   ```bash
   cd frontend
   npm install
   npm start
   ```

3. **Access Application**:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8001
   - API Docs: http://localhost:8001/docs

## 🧪 Testing

### Comprehensive Test Suite (`backend_test.py`)
- **API Endpoint Testing**: All 12 endpoints validated
- **File Upload Testing**: Various file sizes and types
- **Error Handling**: Invalid inputs and edge cases
- **Concurrent Upload Testing**: Multiple simultaneous uploads
- **Performance Validation**: Resource limits and bandwidth control

### Test Results Summary
Based on `test_result.md`, the backend has been comprehensively tested with all core functionality working correctly. The testing protocol includes:
- FastAPI server endpoints ✅
- Multi-service upload integration ✅
- Concurrent upload management ✅
- CSV export functionality ✅
- In-memory storage system ✅

## 📊 System Monitoring

### Real-time Metrics
- **CPU Usage**: Percentage with color-coded indicators
- **Memory Usage**: RAM consumption tracking
- **Active Uploads**: Current vs. maximum concurrent uploads
- **Service Status**: Configuration status for all 5 services
- **Upload History**: Complete upload tracking with results

### Logging System
- **System Logs**: Application-wide events and errors
- **Upload Logs**: Per-upload detailed progress tracking
- **Log Retention**: Last 100 system logs, 50 logs per upload
- **Log Levels**: INFO, SUCCESS, WARNING, ERROR

## 🔒 Security & Safety

### File Validation
- **Size Limits**: 100 bytes minimum, 15GB maximum
- **Type Validation**: Video file extensions and MIME types
- **Malicious File Protection**: Extension and content validation

### System Protection
- **Resource Monitoring**: Prevents system overload
- **Upload Limits**: Configurable concurrent upload restrictions
- **Emergency Stop**: "Clear All" functionality for immediate halt
- **Automatic Cleanup**: Temporary file removal and memory management

### Data Privacy
- **No Database**: All data stored in memory only
- **Local Processing**: Files processed locally before upload
- **API Key Security**: Environment variable storage
- **Temporary Files**: Automatic cleanup after processing

## 🔄 Workflow

### Upload Process
1. **File Selection**: Drag-and-drop or file browser
2. **Validation**: Size, type, and format checking
3. **Queue Management**: Add to upload queue
4. **Background Processing**: Async upload to all services
5. **Progress Tracking**: Real-time status updates
6. **Result Compilation**: Collect embed codes from all services
7. **CSV Generation**: Create downloadable embed code file
8. **Cleanup**: Remove temporary files and update status

### Error Handling
- **Service Failures**: Continue with other services if one fails
- **Network Issues**: Retry logic and timeout handling
- **File Errors**: Detailed error messages and logging
- **System Overload**: Automatic throttling and resource management

## 📈 Performance Characteristics

### Optimizations
- **Memory Efficient**: Chunked file processing
- **CPU Friendly**: Limited concurrent operations
- **Network Optimized**: Bandwidth limiting and connection pooling
- **Storage Efficient**: In-memory storage with automatic cleanup

### Scalability Considerations
- **Horizontal Scaling**: Multiple backend instances possible
- **Load Balancing**: Stateless design supports load distribution
- **Resource Scaling**: Configurable limits based on system capacity
- **Service Expansion**: Easy addition of new video hosting services

## 🛠️ Development Notes

### Code Quality
- **Type Hints**: Python type annotations throughout
- **Async/Await**: Modern async programming patterns
- **Error Handling**: Comprehensive exception management
- **Logging**: Detailed logging for debugging and monitoring

### Extensibility
- **Service Addition**: Easy integration of new video hosts
- **Configuration**: Environment-based configuration
- **API Expansion**: RESTful API design for easy extension
- **UI Enhancement**: Component-based React architecture

## 📝 Known Limitations

1. **API Key Dependency**: Requires valid API keys for full functionality
2. **Memory Storage**: Data lost on application restart
3. **Single Instance**: No built-in clustering or load balancing
4. **File Size**: Limited by available system memory and storage
5. **Network Dependency**: Requires stable internet for uploads

## 🔮 Future Enhancements

### Potential Improvements
- **Database Integration**: Persistent storage option
- **User Authentication**: Multi-user support
- **Upload Scheduling**: Delayed and scheduled uploads
- **Bandwidth Monitoring**: Real-time network usage tracking
- **Mobile App**: Native mobile application
- **Cloud Storage**: Integration with cloud storage providers
- **Analytics Dashboard**: Upload statistics and reporting

## 📚 API Reference

### Upload Endpoints

#### POST /api/upload
Upload a video file to all configured services.

**Request:**
- Content-Type: `multipart/form-data`
- Fields:
  - `file`: Video file (required)
  - `title`: Video title (optional)

**Response:**
```json
{
  "success": true,
  "upload_id": "uuid-string",
  "filename": "video.mp4",
  "file_size": 1048576,
  "message": "Upload started successfully"
}
```

#### GET /api/status/{upload_id}
Get upload progress and status.

**Response:**
```json
{
  "upload_id": "uuid-string",
  "filename": "video.mp4",
  "status": "completed",
  "progress": 100,
  "file_size": 1048576,
  "created_at": "2024-01-01T00:00:00",
  "csv_available": true,
  "results": {
    "lulustream": {"embed_code": "iframe..."},
    "streamp2p": {"embed_code": "iframe..."}
  }
}
```

### Management Endpoints

#### GET /api/uploads
List all upload records.

**Response:**
```json
{
  "uploads": [
    {
      "upload_id": "uuid-string",
      "filename": "video.mp4",
      "status": "completed",
      "file_size": 1048576,
      "created_at": "2024-01-01T00:00:00",
      "csv_available": true
    }
  ]
}
```

#### POST /api/cancel/{upload_id}
Cancel an active upload.

**Response:**
```json
{
  "success": true,
  "message": "Upload cancelled successfully"
}
```

#### POST /api/clear-all
Clear all uploads, logs, and temporary data.

**Response:**
```json
{
  "success": true,
  "message": "All data cleared successfully"
}
```

### Monitoring Endpoints

#### GET /api/system-status
Get system resource usage and configuration.

**Response:**
```json
{
  "cpu_usage": 45.2,
  "memory_usage": 67.8,
  "memory_available": 8589934592,
  "disk_usage": 23.4,
  "disk_free": 107374182400,
  "active_uploads": 2,
  "max_concurrent": 3,
  "bandwidth_limit": 70,
  "services_configured": 5,
  "total_uploads": 15,
  "system_logs_count": 42
}
```

#### GET /api/logs/{upload_id}
Get logs for a specific upload.

**Response:**
```json
{
  "upload_id": "uuid-string",
  "logs": [
    {
      "timestamp": "2024-01-01T00:00:00",
      "level": "INFO",
      "message": "Upload initiated for video.mp4"
    }
  ]
}
```

#### GET /api/system-logs
Get system-wide logs.

**Response:**
```json
{
  "logs": [
    {
      "timestamp": "2024-01-01T00:00:00",
      "level": "INFO",
      "message": "Video Upload Automation API started"
    }
  ]
}
```

## 🔧 Service Integration Details

### Service Configuration
Each video hosting service requires specific API configuration:

#### Lulustream
- **API Endpoint**: `https://lulustream.com/api/upload/server`
- **Upload Method**: POST with multipart form data
- **Required Fields**: `key`, `file_title`, `file`
- **Response Format**: JSON with `filecode`
- **Embed Format**: `<iframe src="https://luluvid.com/e/{filecode}"...>`

#### StreamP2P
- **API Endpoint**: `https://streamp2p.com/api/v1/video/upload`
- **Authentication**: Bearer token
- **Upload Method**: POST with multipart form data
- **Response Format**: JSON with `video_id`
- **Embed Format**: `<iframe src="https://streamdb.p2pstream.online/#{video_id}"...>`

#### RPMShare
- **API Endpoint**: `https://rpmshare.com/api/v1/video/upload`
- **Authentication**: Bearer token
- **Upload Method**: POST with multipart form data
- **Response Format**: JSON with `video_id`
- **Embed Format**: `<iframe src="https://streamdb.rpmstream.online/#{video_id}"...>`

#### Filemoon
- **API Endpoint**: `https://filemoon.to/api/upload`
- **Authentication**: API key in form data
- **Upload Method**: POST with multipart form data
- **Required Fields**: `api_key`, `file`
- **Response Format**: JSON with `file_id`
- **Embed Format**: `<iframe src="https://filemoon.to/e/{file_id}/{filename}"...>`

#### UpnShare
- **API Endpoint**: `https://upnshare.com/api/v1/video/upload`
- **Authentication**: Bearer token
- **Upload Method**: POST with multipart form data
- **Response Format**: JSON with `video_id`
- **Embed Format**: `<iframe src="https://streamdb.upns.online/#{video_id}"...>`

## 📋 Dependencies

### Backend Dependencies (`requirements.txt`)
```
fastapi==0.104.1          # Web framework
uvicorn[standard]==0.24.0 # ASGI server
python-multipart==0.0.6   # File upload support
aiofiles==23.2.1          # Async file operations
aiohttp==3.9.1            # HTTP client
pymongo==4.6.0            # MongoDB driver (unused in current version)
psutil==5.9.6             # System monitoring
python-dotenv==1.0.0      # Environment variables
```

### Frontend Dependencies (`package.json`)
```json
{
  "dependencies": {
    "axios": "^1.8.4",           // HTTP client (unused)
    "react": "^19.0.0",          // React framework
    "react-dom": "^19.0.0",      // React DOM
    "react-router-dom": "^7.5.1", // Routing (unused)
    "react-scripts": "5.0.1"     // Build scripts
  },
  "devDependencies": {
    "@craco/craco": "^7.1.0",    // Build customization
    "tailwindcss": "^3.4.17",    // CSS framework
    "autoprefixer": "^10.4.20",  // CSS processing
    "postcss": "^8.4.49"         // CSS processing
  }
}
```

## 🔍 Error Codes and Handling

### HTTP Status Codes
- **200**: Success
- **400**: Bad Request (invalid file, missing data)
- **404**: Not Found (upload ID not found)
- **500**: Internal Server Error

### Common Error Scenarios
1. **File Too Large**: Files exceeding 15GB limit
2. **File Too Small**: Files under 100 bytes
3. **Invalid File Type**: Non-video files
4. **Missing API Keys**: Service configuration errors
5. **Network Timeouts**: Upload service unavailable
6. **Disk Space**: Insufficient temporary storage

### Error Response Format
```json
{
  "detail": "Error message description"
}
```

## 🎨 UI Components and Features

### Main Interface Components
1. **Header Bar**: System status indicators and controls
2. **Upload Zone**: Drag-and-drop file area
3. **File Queue**: Pending uploads with individual controls
4. **Upload History**: Completed uploads with results
5. **Service Status**: Configuration status for all services
6. **Logs Modal**: System and upload-specific logging

### Responsive Design
- **Mobile Optimized**: Touch-friendly interface
- **Tablet Support**: Optimized layout for medium screens
- **Desktop**: Full-featured interface with all controls
- **Dark Mode Ready**: Prepared for dark theme implementation

### User Experience Features
- **Visual Feedback**: Color-coded status indicators
- **Progress Tracking**: Real-time upload progress
- **Error Messages**: User-friendly error descriptions
- **Confirmation Dialogs**: Prevent accidental actions
- **Keyboard Navigation**: Accessible interface design

---

*This documentation provides a comprehensive overview of the Video Upload Automation project. For detailed setup instructions, refer to `LOCAL_SETUP_GUIDE.md`. For testing information, see `test_result.md`.*
