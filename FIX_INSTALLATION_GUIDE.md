# 🛠️ Fix for Python Package Installation Error

## 🚨 **Error Analysis**
The installation is failing because `psutil` requires Microsoft Visual C++ 14.0 or greater to compile on Windows.

## ✅ **SOLUTION 1: Install Visual C++ Build Tools (Recommended)**

### Step 1: Download Visual Studio Build Tools
1. Go to: https://visualstudio.microsoft.com/visual-cpp-build-tools/
2. Download "Build Tools for Visual Studio 2022"
3. Run the installer

### Step 2: Install Required Components
In the Visual Studio Installer:
1. Select "C++ build tools" workload
2. Make sure these are checked:
   - ✅ MSVC v143 - VS 2022 C++ x64/x86 build tools
   - ✅ Windows 10/11 SDK (latest version)
   - ✅ CMake tools for Visual Studio
3. Click "Install"

### Step 3: Restart and Retry
```bash
# After installation, restart your computer
# Then try again:
cd backend
venv\Scripts\activate
pip install -r requirements.txt
```

## ✅ **SOLUTION 2: Use Pre-compiled Wheels (Faster Alternative)**

### Option A: Upgrade pip and use pre-compiled packages
```bash
cd backend
venv\Scripts\activate
python -m pip install --upgrade pip
pip install --upgrade setuptools wheel
pip install -r requirements.txt
```

### Option B: Install packages individually with pre-compiled wheels
```bash
cd backend
venv\Scripts\activate
pip install --upgrade pip
pip install fastapi==0.104.1
pip install "uvicorn[standard]==0.24.0"
pip install python-multipart==0.0.6
pip install aiofiles==23.2.1
pip install aiohttp==3.9.1
pip install pymongo==4.6.0
pip install psutil==5.9.6 --only-binary=all
pip install python-dotenv==1.0.0
```

## ✅ **SOLUTION 3: Alternative Requirements (If above fails)**

Create a new requirements file with alternative packages:
```bash
# Create requirements-alt.txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
aiofiles==23.2.1
aiohttp==3.9.1
pymongo==4.6.0
python-dotenv==1.0.0
# psutil - commented out, will use alternative system monitoring
```

Then install:
```bash
cd backend
venv\Scripts\activate
pip install -r requirements-alt.txt
```

## ✅ **SOLUTION 4: Use Conda Instead of pip (Alternative)**

If you have Anaconda or Miniconda installed:
```bash
# Create conda environment
conda create -n video-upload python=3.9
conda activate video-upload

# Install packages via conda (has pre-compiled binaries)
conda install -c conda-forge fastapi uvicorn aiofiles aiohttp pymongo psutil python-dotenv
pip install python-multipart
```

## 🔧 **Quick Fix Commands (Try These First)**

```bash
# Navigate to backend directory
cd "G:\My Websites\Catalogue-Website\video-upload-automation\video-upload-automation\backend"

# Activate virtual environment
venv\Scripts\activate

# Upgrade pip and tools
python -m pip install --upgrade pip setuptools wheel

# Try installing with pre-compiled wheels only
pip install --only-binary=all -r requirements.txt

# If that fails, try without psutil first
pip install fastapi==0.104.1 uvicorn[standard]==0.24.0 python-multipart==0.0.6 aiofiles==23.2.1 aiohttp==3.9.1 pymongo==4.6.0 python-dotenv==1.0.0

# Then try psutil separately
pip install psutil --only-binary=all
```

## 🎯 **Expected Success Output**
When successful, you should see:
```
Successfully installed fastapi-0.104.1 uvicorn-0.24.0 python-multipart-0.0.6 aiofiles-23.2.1 aiohttp-3.9.1 pymongo-4.6.0 psutil-5.9.6 python-dotenv-1.0.0
```

## 🐛 **If Problems Persist**

### Check Python Version
```bash
python --version
# Should be Python 3.8 or higher
```

### Try Different Python Version
If you have Python 3.11+, try creating environment with Python 3.9:
```bash
py -3.9 -m venv venv
venv\Scripts\activate
pip install -r requirements.txt
```

### Alternative: Use Python from Microsoft Store
1. Install Python from Microsoft Store (Python 3.11)
2. Create new virtual environment
3. Try installation again

## ✅ **Verification Steps**

After successful installation:
```bash
# Test if packages are installed correctly
python -c "import fastapi; print('FastAPI:', fastapi.__version__)"
python -c "import uvicorn; print('Uvicorn installed successfully')"
python -c "import psutil; print('psutil:', psutil.__version__)"
python -c "import aiofiles; print('aiofiles installed successfully')"
python -c "import aiohttp; print('aiohttp installed successfully')"
python -c "import pymongo; print('pymongo installed successfully')"
python -c "import dotenv; print('python-dotenv installed successfully')"
```

## 🚀 **Next Steps After Fix**

Once packages are installed successfully:
```bash
# Start the backend server
python server.py
```

You should see:
```
INFO:     Started server process
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8001
```

## 📞 **Still Having Issues?**

If none of these solutions work:
1. Share the exact error message you're getting
2. Check your Python version: `python --version`
3. Try using a different Python installation
4. Consider using Docker for the backend instead